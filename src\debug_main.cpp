/*
 * PowMr Inverter Debug Version
 * 
 * This version includes extensive debugging output to help troubleshoot
 * communication issues with the PowMr inverter.
 */

#include <HardwareSerial.h>
#include <ModbusRTU.h>

// Hardware configuration
#define RXD2 16
#define TXD2 17

// Communication settings to try
uint8_t slaveIds[] = {1, 5, 10, 247};
uint32_t baudRates[] = {2400, 9600, 4800, 19200};
uint16_t testRegisters[] = {201, 202, 215, 219, 4501, 4502};

ModbusRTU mb;

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=== PowMr Inverter Debug Tool ===");
  Serial.println("This tool will systematically test different communication parameters");
  Serial.println("Expected values: Battery ~53.8V, Input ~28V");
  Serial.println();
  
  runDebugTests();
}

void loop() {
  // Debug runs once in setup
  delay(10000);
}

void runDebugTests() {
  Serial.println("Starting systematic communication tests...\n");
  
  for (int b = 0; b < 4; b++) {
    for (int s = 0; s < 4; s++) {
      uint32_t baud = baudRates[b];
      uint8_t slaveId = slaveIds[s];
      
      Serial.println("========================================");
      Serial.print("Testing Baud Rate: ");
      Serial.print(baud);
      Serial.print(", Slave ID: ");
      Serial.println(slaveId);
      Serial.println("========================================");
      
      // Initialize communication
      Serial2.end();
      delay(100);
      Serial2.begin(baud, SERIAL_8N1, RXD2, TXD2);
      mb.begin(&Serial2);
      delay(500);
      
      // Test different register addresses
      testRegisterAddresses(slaveId);
      
      delay(1000);
    }
  }
  
  Serial.println("\n=== Debug Test Complete ===");
  Serial.println("If you found working values, update the main code accordingly.");
}

void testRegisterAddresses(uint8_t slaveId) {
  for (int r = 0; r < 6; r++) {
    uint16_t regAddr = testRegisters[r];
    
    Serial.print("  Testing register ");
    Serial.print(regAddr);
    Serial.print(": ");
    
    uint16_t data[5];
    bool success = mb.readHreg(slaveId, regAddr, data, 5, nullptr);
    
    if (success) {
      Serial.print("SUCCESS - Values: ");
      for (int i = 0; i < 5; i++) {
        Serial.print(data[i]);
        if (i < 4) Serial.print(", ");
      }
      Serial.println();
      
      // Analyze values for battery voltage patterns
      analyzeValues(regAddr, data);
      
    } else {
      Serial.println("FAILED");
    }
    
    delay(200);
  }
}

void analyzeValues(uint16_t regAddr, uint16_t* data) {
  // Look for battery voltage pattern (53.8V)
  for (int i = 0; i < 5; i++) {
    float voltage1 = data[i] * 0.1;   // Scale 0.1
    float voltage2 = data[i] * 0.01;  // Scale 0.01
    
    // Check if any value matches expected battery voltage (53.8V ±2V)
    if ((voltage1 >= 51.8 && voltage1 <= 55.8) || 
        (voltage2 >= 51.8 && voltage2 <= 55.8)) {
      Serial.print("    *** POTENTIAL BATTERY VOLTAGE FOUND! ***");
      Serial.print(" Reg ");
      Serial.print(regAddr + i);
      Serial.print(": ");
      Serial.print(data[i]);
      Serial.print(" (");
      Serial.print(voltage1, 1);
      Serial.print("V with 0.1 scale, ");
      Serial.print(voltage2, 2);
      Serial.println("V with 0.01 scale)");
    }
    
    // Check for input voltage pattern (28V ±5V)
    if ((voltage1 >= 23 && voltage1 <= 33) || 
        (voltage2 >= 23 && voltage2 <= 33)) {
      Serial.print("    *** POTENTIAL INPUT VOLTAGE FOUND! ***");
      Serial.print(" Reg ");
      Serial.print(regAddr + i);
      Serial.print(": ");
      Serial.print(data[i]);
      Serial.print(" (");
      Serial.print(voltage1, 1);
      Serial.print("V with 0.1 scale, ");
      Serial.print(voltage2, 2);
      Serial.println("V with 0.01 scale)");
    }
  }
}

// Additional test function for raw data inspection
void testRawCommunication() {
  Serial.println("\n=== Raw Communication Test ===");
  
  // Test with most common settings first
  Serial2.begin(2400, SERIAL_8N1, RXD2, TXD2);
  mb.begin(&Serial2);
  delay(1000);
  
  // Try to read a range of registers
  for (uint16_t reg = 200; reg <= 250; reg++) {
    uint16_t data[1];
    if (mb.readHreg(5, reg, data, 1, nullptr)) {
      Serial.print("Register ");
      Serial.print(reg);
      Serial.print(": ");
      Serial.print(data[0]);
      Serial.print(" (");
      Serial.print(data[0] * 0.1, 1);
      Serial.print("V, ");
      Serial.print(data[0] * 0.01, 2);
      Serial.println("V)");
    }
    delay(100);
  }
}
