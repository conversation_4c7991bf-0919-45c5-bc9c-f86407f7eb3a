/*
 * PowMr Inverter Debug Version
 * 
 * This version includes extensive debugging output to help troubleshoot
 * communication issues with the PowMr inverter.
 */

#include <HardwareSerial.h>
#include <ModbusRTU.h>

// Hardware configuration
#define RXD2 16
#define TXD2 17

// Communication settings to try
uint8_t slaveIds[] = {1, 5, 10, 247};
uint32_t baudRates[] = {2400, 9600, 4800, 19200};

// Extended register list to test
uint16_t testRegisters[] = {
  // SMG-II protocol registers
  201, 202, 203, 204, 205, 206, 207, 208, 209, 210,
  211, 212, 213, 214, 215, 216, 217, 218, 219, 220,
  221, 222, 223, 224, 225, 226, 227, 228, 229, 230,
  231, 232, 233, 234,

  // PowMr original registers
  4501, 4502, 4503, 4504, 4505, 4506, 4507, 4508, 4509, 4510,

  // ESPHome style registers
  0x3000, 0x3001, 0x3002, 0x3003, 0x3004, 0x3005,

  // Alternative addressing
  12289, 12290, 12291, 12292, 12293, 12294
};

ModbusRTU mb;

// Function prototypes
void runDebugTests();
void testRegisterAddresses(uint8_t slaveId);
void testExtendedRange();
void analyzeValues(uint16_t regAddr, uint16_t* data);
void testRawCommunication();

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=== PowMr Inverter Debug Tool ===");
  Serial.println("This tool will systematically test different communication parameters");
  Serial.println("Expected values: Battery ~53.8V, Input ~28V");
  Serial.println();
  
  runDebugTests();
}

void loop() {
  // Debug runs once in setup
  delay(10000);
}

void runDebugTests() {
  Serial.println("Starting systematic communication tests...\n");
  Serial.println("Looking for Battery ~53.8V and Input ~28V");
  Serial.println("Testing with different scaling factors...\n");

  // Test only the most promising combinations first
  uint32_t priorityBauds[] = {2400, 9600};
  uint8_t prioritySlaves[] = {1, 5};

  for (int b = 0; b < 2; b++) {
    for (int s = 0; s < 2; s++) {
      uint32_t baud = priorityBauds[b];
      uint8_t slaveId = prioritySlaves[s];

      Serial.println("========================================");
      Serial.print("Testing Baud Rate: ");
      Serial.print(baud);
      Serial.print(", Slave ID: ");
      Serial.println(slaveId);
      Serial.println("========================================");

      // Initialize communication
      Serial2.end();
      delay(100);
      Serial2.begin(baud, SERIAL_8N1, RXD2, TXD2);
      mb.begin(&Serial2);
      delay(500);

      // Test different register addresses
      testRegisterAddresses(slaveId);

      delay(1000);
    }
  }

  Serial.println("\n=== Priority Test Complete ===");
  Serial.println("Now testing extended register range...\n");

  // Test extended range with best parameters
  testExtendedRange();

  Serial.println("\n=== Debug Test Complete ===");
  Serial.println("If you found working values, update the main code accordingly.");
}

void testRegisterAddresses(uint8_t slaveId) {
  // Test key registers first
  uint16_t keyRegisters[] = {201, 202, 215, 219, 4501, 4502};

  for (int r = 0; r < 6; r++) {
    uint16_t regAddr = keyRegisters[r];

    Serial.print("  Testing register ");
    Serial.print(regAddr);
    Serial.print(": ");

    uint16_t data[3];
    bool success = mb.readHreg(slaveId, regAddr, data, 3, nullptr);

    if (success) {
      Serial.print("SUCCESS - Values: ");
      for (int i = 0; i < 3; i++) {
        Serial.print(data[i]);
        if (i < 2) Serial.print(", ");
      }
      Serial.println();

      // Analyze values for battery voltage patterns
      analyzeValues(regAddr, data);

    } else {
      Serial.println("FAILED");
    }

    delay(200);
  }
}

void testExtendedRange() {
  Serial.println("Testing extended register range with Baud 2400, Slave ID 5...");

  // Initialize with most common settings
  Serial2.end();
  delay(100);
  Serial2.begin(2400, SERIAL_8N1, RXD2, TXD2);
  mb.begin(&Serial2);
  delay(500);

  // Test range 200-250 (SMG-II range)
  Serial.println("\nTesting SMG-II range (200-250):");
  for (uint16_t reg = 200; reg <= 250; reg += 5) {
    uint16_t data[1];
    if (mb.readHreg(5, reg, data, 1, nullptr)) {
      Serial.print("Reg ");
      Serial.print(reg);
      Serial.print(": ");
      Serial.print(data[0]);

      // Check for potential battery voltage
      float v1 = data[0] * 0.1;
      float v2 = data[0] * 0.01;

      if ((v1 >= 50 && v1 <= 60) || (v2 >= 50 && v2 <= 60)) {
        Serial.print(" *** POTENTIAL BATTERY! ");
        Serial.print(v1, 1);
        Serial.print("V or ");
        Serial.print(v2, 2);
        Serial.print("V ***");
      }

      // Check for potential input voltage
      if ((v1 >= 25 && v1 <= 35) || (v2 >= 25 && v2 <= 35)) {
        Serial.print(" *** POTENTIAL INPUT! ");
        Serial.print(v1, 1);
        Serial.print("V or ");
        Serial.print(v2, 2);
        Serial.print("V ***");
      }

      Serial.println();
    }
    delay(100);
  }

  // Test ESPHome style addresses
  Serial.println("\nTesting ESPHome style addresses:");
  uint16_t espAddresses[] = {0x3000, 0x3001, 0x3002, 0x3003, 0x3004, 0x3005, 0x3006, 0x3007, 0x3008, 0x3009};
  for (int i = 0; i < 10; i++) {
    uint16_t data[1];
    if (mb.readHreg(5, espAddresses[i], data, 1, nullptr)) {
      Serial.print("Reg 0x");
      Serial.print(espAddresses[i], HEX);
      Serial.print(": ");
      Serial.print(data[0]);

      float v1 = data[0] * 0.1;
      float v2 = data[0] * 0.01;

      if ((v1 >= 50 && v1 <= 60) || (v2 >= 50 && v2 <= 60)) {
        Serial.print(" *** POTENTIAL BATTERY! ***");
      }
      if ((v1 >= 25 && v1 <= 35) || (v2 >= 25 && v2 <= 35)) {
        Serial.print(" *** POTENTIAL INPUT! ***");
      }

      Serial.println();
    }
    delay(100);
  }
}

void analyzeValues(uint16_t regAddr, uint16_t* data) {
  // Look for battery voltage pattern (53.8V)
  for (int i = 0; i < 3; i++) {
    float voltage1 = data[i] * 0.1;   // Scale 0.1
    float voltage2 = data[i] * 0.01;  // Scale 0.01
    float voltage3 = data[i] * 0.001; // Scale 0.001
    float voltage4 = data[i] / 10.0;  // Divide by 10
    float voltage5 = data[i] / 100.0; // Divide by 100

    // Check if any value matches expected battery voltage (53.8V ±3V)
    if ((voltage1 >= 50.8 && voltage1 <= 56.8) ||
        (voltage2 >= 50.8 && voltage2 <= 56.8) ||
        (voltage3 >= 50.8 && voltage3 <= 56.8) ||
        (voltage4 >= 50.8 && voltage4 <= 56.8) ||
        (voltage5 >= 50.8 && voltage5 <= 56.8)) {
      Serial.print("    *** POTENTIAL BATTERY VOLTAGE! *** Reg ");
      Serial.print(regAddr + i);
      Serial.print(": ");
      Serial.print(data[i]);
      Serial.print(" (");
      Serial.print(voltage1, 1); Serial.print("V*0.1, ");
      Serial.print(voltage2, 2); Serial.print("V*0.01, ");
      Serial.print(voltage4, 1); Serial.print("V/10)");
      Serial.println();
    }

    // Check for input voltage pattern (28V ±7V)
    if ((voltage1 >= 21 && voltage1 <= 35) ||
        (voltage2 >= 21 && voltage2 <= 35) ||
        (voltage4 >= 21 && voltage4 <= 35) ||
        (voltage5 >= 21 && voltage5 <= 35)) {
      Serial.print("    *** POTENTIAL INPUT VOLTAGE! *** Reg ");
      Serial.print(regAddr + i);
      Serial.print(": ");
      Serial.print(data[i]);
      Serial.print(" (");
      Serial.print(voltage1, 1); Serial.print("V*0.1, ");
      Serial.print(voltage2, 2); Serial.print("V*0.01, ");
      Serial.print(voltage4, 1); Serial.print("V/10)");
      Serial.println();
    }

    // Check for reasonable values that might be scaled differently
    if (data[i] >= 538 && data[i] <= 540) {
      Serial.print("    *** EXACT BATTERY MATCH! *** Reg ");
      Serial.print(regAddr + i);
      Serial.print(": ");
      Serial.print(data[i]);
      Serial.println(" (53.8V with 0.1 scale)");
    }

    if (data[i] >= 280 && data[i] <= 285) {
      Serial.print("    *** EXACT INPUT MATCH! *** Reg ");
      Serial.print(regAddr + i);
      Serial.print(": ");
      Serial.print(data[i]);
      Serial.println(" (28V with 0.1 scale)");
    }
  }
}

// Additional test function for raw data inspection
void testRawCommunication() {
  Serial.println("\n=== Raw Communication Test ===");
  
  // Test with most common settings first
  Serial2.begin(2400, SERIAL_8N1, RXD2, TXD2);
  mb.begin(&Serial2);
  delay(1000);
  
  // Try to read a range of registers
  for (uint16_t reg = 200; reg <= 250; reg++) {
    uint16_t data[1];
    if (mb.readHreg(5, reg, data, 1, nullptr)) {
      Serial.print("Register ");
      Serial.print(reg);
      Serial.print(": ");
      Serial.print(data[0]);
      Serial.print(" (");
      Serial.print(data[0] * 0.1, 1);
      Serial.print("V, ");
      Serial.print(data[0] * 0.01, 2);
      Serial.println("V)");
    }
    delay(100);
  }
}
