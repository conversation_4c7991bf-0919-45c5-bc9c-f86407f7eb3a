/*
 * PowMr 6.2kW Inverter Basic Usage Example
 * 
 * This example demonstrates basic usage of the PowMr Modbus RTU library
 * for reading inverter data and controlling basic functions.
 */

#include <HardwareSerial.h>
#include <ModbusRTU.h>

// Hardware configuration
#define RXD2 16
#define TXD2 17

// Communication settings
#define INVERTER_ID 5
#define BAUD_RATE 2400

ModbusRTU mb;

void setup() {
  Serial.begin(115200);
  Serial.println("PowMr Basic Usage Example");
  
  // Initialize Modbus communication
  Serial2.begin(BAUD_RATE, SERIAL_8N1, RXD2, TXD2);
  mb.begin(&Serial2);
  
  delay(2000);
}

void loop() {
  // Read basic inverter data
  readBasicData();
  
  // Example: Set charge current to 30A (uncomment to use)
  // setChargeCurrentExample();
  
  delay(10000); // Read every 10 seconds
}

void readBasicData() {
  uint16_t data[20];
  
  // Read main status registers (4501-4520)
  if (mb.readHreg(INVERTER_ID, 4501, data, 20, nullptr)) {
    Serial.println("\n=== PowMr Inverter Data ===");
    
    // Battery information
    float batteryVoltage = data[0] * 0.01;
    float batteryCurrent = data[1] * 0.01;
    uint16_t batteryCapacity = data[2];
    
    Serial.print("Battery Voltage: ");
    Serial.print(batteryVoltage, 2);
    Serial.println(" V");
    
    Serial.print("Battery Current: ");
    Serial.print(batteryCurrent, 2);
    Serial.println(" A");
    
    Serial.print("Battery Capacity: ");
    Serial.print(batteryCapacity);
    Serial.println(" %");
    
    // Solar PV information
    float pvVoltage = data[8] * 0.1;
    float pvCurrent = data[9] * 0.01;
    uint16_t pvPower = data[10];
    
    Serial.print("PV Voltage: ");
    Serial.print(pvVoltage, 1);
    Serial.println(" V");
    
    Serial.print("PV Current: ");
    Serial.print(pvCurrent, 2);
    Serial.println(" A");
    
    Serial.print("PV Power: ");
    Serial.print(pvPower);
    Serial.println(" W");
    
    // Grid information
    float gridVoltage = data[6] * 0.1;
    float gridFrequency = data[7] * 0.01;
    
    Serial.print("Grid Voltage: ");
    Serial.print(gridVoltage, 1);
    Serial.println(" V");
    
    Serial.print("Grid Frequency: ");
    Serial.print(gridFrequency, 2);
    Serial.println(" Hz");
    
    // Inverter output
    float outputVoltage = data[3] * 0.1;
    float outputCurrent = data[4] * 0.01;
    uint16_t outputPower = data[5];
    
    Serial.print("Output Voltage: ");
    Serial.print(outputVoltage, 1);
    Serial.println(" V");
    
    Serial.print("Output Current: ");
    Serial.print(outputCurrent, 2);
    Serial.println(" A");
    
    Serial.print("Output Power: ");
    Serial.print(outputPower);
    Serial.println(" W");
    
    Serial.println("==========================");
    
  } else {
    Serial.println("Failed to read inverter data");
  }
}

void setChargeCurrentExample() {
  // Set charge current to 30A
  // Register 5024: Utility charge current
  uint16_t chargeCurrent = 30;
  
  if (mb.writeHreg(INVERTER_ID, 5024, chargeCurrent, nullptr)) {
    Serial.print("Successfully set charge current to ");
    Serial.print(chargeCurrent);
    Serial.println(" A");
  } else {
    Serial.println("Failed to set charge current");
  }
}

void setOutputPriorityExample() {
  // Set output priority to Solar first
  // Register 5018: Output source priority
  // 0: Solar first, 1: Mains first, 2: Battery first
  uint16_t priority = 0; // Solar first
  
  if (mb.writeHreg(INVERTER_ID, 5018, priority, nullptr)) {
    Serial.println("Successfully set output priority to Solar first");
  } else {
    Serial.println("Failed to set output priority");
  }
}

void setBuzzerExample() {
  // Disable buzzer alarm
  // Register 5002: Buzzer alarm (0=Off, 1=On)
  uint16_t buzzerState = 0; // Off
  
  if (mb.writeHreg(INVERTER_ID, 5002, buzzerState, nullptr)) {
    Serial.println("Successfully disabled buzzer alarm");
  } else {
    Serial.println("Failed to set buzzer state");
  }
}
