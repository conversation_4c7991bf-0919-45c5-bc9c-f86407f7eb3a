# PowMr 6.2kW Inverter Modbus RTU Monitor

โปรเจกต์นี้เป็นโค้ด ESP32 สำหรับเชื่อมต่อและตรวจสอบข้อมูลจาก PowMr 6.2kW Inverter ผ่าน RS232 ด้วยโปรโตคอล Modbus RTU

## คุณสมบัติ

### การตรวจสอบข้อมูล
- **ข้อมูลแบตเตอรี่**: แรงดัน, กระแส, ความจุ (%)
- **ข้อมูล Solar PV**: แรงดัน, กระแส, กำลังไฟฟ้า
- **ข้อมูลกริด**: แรงดัน, ความถี่
- **ข้อมูล Inverter Output**: แรงดัน, กระแส, กำลังไฟฟ้า
- **ข้อมูลเพิ่มเติม**: อุณหภูมิ, สถานะ, รหัสข้อผิดพลาด

### การควบคุม
- **ตั้งค่ากระแสชาร์จ**: 2, 10, 20, 30, 40, 50, 60 แอมป์
- **ตั้งค่าลำดับความสำคัญ Output**: Solar first, Mains first, Battery first
- **ตั้งค่าลำดับความสำคัญ Charger**: Solar first, Mains first, Solar + Mains, Solar only
- **ควบคุม Buzzer**: เปิด/ปิดเสียงแจ้งเตือน

### ฟีเจอร์เพิ่มเติม
- **Auto-detection**: ตรวจหา Slave ID และ Baud Rate อัตโนมัติ
- **Connection Recovery**: เชื่อมต่อใหม่อัตโนมัติเมื่อขาดการเชื่อมต่อ
- **Data Validation**: กรองข้อมูลที่ไม่ถูกต้อง
- **Serial Commands**: ควบคุมผ่าน Serial Monitor

## การเชื่อมต่อฮาร์ดแวร์

### ESP32 Pins
- **RX (GPIO16)**: เชื่อมต่อกับ TX ของ RS232 converter
- **TX (GPIO17)**: เชื่อมต่อกับ RX ของ RS232 converter

### PowMr Inverter RJ45 Connector
```
Pin 1 (White Orange) - TX (data from inverter)
Pin 2 (Orange)       - RX (data to inverter)  
Pin 4 (Blue)         - +12V
Pin 8 (<PERSON>)        - Ground
```

### RS232 Level Converter
ใช้ MAX3232 หรือ SP3232 converter เพื่อแปลงระดับสัญญาณ TTL (3.3V) เป็น RS232

## การติดตั้ง

### 1. ติดตั้ง PlatformIO
```bash
pip install platformio
```

### 2. Clone โปรเจกต์
```bash
git clone <repository-url>
cd testESP
```

### 3. Build และ Upload
```bash
pio run -t upload
```

### 4. Monitor Serial Output
```bash
pio device monitor
```

## การใช้งาน

### คำสั่งผ่าน Serial Monitor

| คำสั่ง | คำอธิบาย |
|--------|----------|
| `help` หรือ `h` | แสดงคำสั่งที่ใช้ได้ |
| `status` หรือ `s` | แสดงสถานะการเชื่อมต่อ |
| `reconnect` | บังคับเชื่อมต่อใหม่ |
| `set_charge_current <value>` | ตั้งค่ากระแสชาร์จ (2,10,20,30,40,50,60) |
| `set_output_priority <value>` | ตั้งค่าลำดับ output (0=Solar, 1=Mains, 2=Battery) |
| `set_charger_priority <value>` | ตั้งค่าลำดับ charger (0=Solar, 1=Mains, 2=Both, 3=Solar only) |
| `buzzer_on` | เปิดเสียงแจ้งเตือน |
| `buzzer_off` | ปิดเสียงแจ้งเตือน |

### ตัวอย่างการใช้งาน
```
set_charge_current 30
set_output_priority 0
buzzer_off
status
```

## การกำหนดค่า

### Communication Settings
```cpp
#define INVERTER_ID_PRIMARY 5      // Slave ID หลัก
#define INVERTER_ID_SECONDARY 1    // Slave ID สำรอง
#define BAUD_RATE_PRIMARY 2400     // Baud rate หลัก
#define BAUD_RATE_SECONDARY 9600   // Baud rate สำรอง
```

### Register Addresses
```cpp
#define REG_STATUS_START 4501      // Main status registers (45 registers)
#define REG_EXTENDED_START 4546    // Extended status registers (16 registers)
#define REG_CONTROL_START 5000     // Control registers
```

## การแก้ไขปัญหา

### ไม่สามารถเชื่อมต่อได้
1. ตรวจสอบการเชื่อมต่อสายไฟ
2. ตรวจสอบ RS232 level converter
3. ลองเปลี่ยน Slave ID (1 หรือ 5)
4. ลองเปลี่ยน Baud Rate (2400 หรือ 9600)

### ข้อมูลไม่ถูกต้อง
1. ตรวจสอบ scaling factors
2. ตรวจสอบ register mapping
3. ดู Serial Monitor สำหรับข้อความ error

### การเชื่อมต่อขาดๆ หายๆ
1. เพิ่ม capacitor ที่ RX pin (GPIO16) เพื่อกรอง RF noise
2. ตรวจสอบการ grounding
3. ลดระยะห่างระหว่าง ESP32 และ Inverter

## ข้อมูลเพิ่มเติม

### Modbus Register Mapping
- **4501-4545**: Main status data (45 registers)
- **4546-4561**: Extended status data (16 registers)  
- **5000+**: Control registers

### Scaling Factors
- **Voltage**: x 0.01 หรือ x 0.1
- **Current**: x 0.01
- **Temperature**: x 0.1
- **Frequency**: x 0.01

## อ้างอิง
- [PowMr Communication Protocol](https://github.com/leodesigner/powmr_comm)
- [ESPHome PowMr Integration](https://github.com/odya/esphome-powmr-hybrid-inverter)
- [Modbus RTU Specification](https://modbus.org/docs/Modbus_Application_Protocol_V1_1b3.pdf)

## License
MIT License
