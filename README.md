# Solar Dashboard ESP32 with LVGL

โปรเจกต์แดชบอร์ดแสดงข้อมูลพลังงานโซล่าเซลสำหรับ ESP32 ที่ใช้ LVGL สำหรับสร้าง UI ที่สวยงามและรองรับการสัมผัส

## คุณสมบัติ

- 🌞 แสดงข้อมูลพลังงานโซล่าเซลแบบเรียลไทม์
- ⚡ แสดงแรงดัน กระแส และกำลังไฟฟ้า
- 🔋 แสดงระดับแบตเตอรี่พร้อม progress bar
- 🌡️ แสดงอุณหภูมิของระบบ
- 📊 Arc gauge แสดงกำลังไฟฟ้าแบบกราฟิก
- 📱 UI ที่สวยงามและรองรับการสัมผัส
- 🎨 การ์ดแสดงข้อมูลแบบ Material Design

## ฮาร์ดแวร์ที่รองรับ

- **MCU**: ESP32
- **หน้าจอ**: ILI9341 TFT 240x320 pixels
- **Touch**: XPT2046 resistive touch controller
- **การเชื่อมต่อ**: SPI

## การเชื่อมต่อขา (Pin Configuration)

```
TFT Display (ILI9341):
- VCC -> 3.3V
- GND -> GND
- CS  -> GPIO 15
- RST -> GPIO 4
- DC  -> GPIO 2
- MOSI-> GPIO 23 (แก้ไขจาก GPIO 13)
- SCK -> GPIO 18 (แก้ไขจาก GPIO 14)
- LED -> GPIO 22
- MISO-> GPIO 19 (แก้ไขจาก GPIO 12)

Touch (XPT2046):
- T_CS -> GPIO 21
- T_IRQ -> (ไม่ใช้)
- T_DIN -> GPIO 23 (ใช้ร่วมกับ MOSI)
- T_OUT -> GPIO 19 (ใช้ร่วมกับ MISO)
- T_CLK -> GPIO 18 (ใช้ร่วมกับ SCK)
```

**หมายเหตุ**: การเปลี่ยนขา SPI เป็น VSPI pins มาตรฐานของ ESP32 ช่วยแก้ปัญหาสัญญาณรบกวนในการแสดงผล

## การติดตั้งและใช้งาน

### 1. ติดตั้ง PlatformIO

```bash
# ติดตั้ง PlatformIO Core
pip install platformio
```

### 2. <PERSON><PERSON> โปรเจกต์

```bash
git clone <repository-url>
cd testESP
```

### 3. Build และ Upload

```bash
# Build โปรเจกต์
pio run

# Upload ไปยัง ESP32
pio run --target upload

# เปิด Serial Monitor
pio device monitor
```

## โครงสร้างโปรเจกต์

```
testESP/
├── src/
│   ├── main.cpp          # โค้ดหลัก
│   └── lv_conf.h         # การตั้งค่า LVGL
├── platformio.ini        # การตั้งค่า PlatformIO
└── README.md            # ไฟล์นี้
```

## การปรับแต่ง

### การเปลี่ยนข้อมูลเซ็นเซอร์

แก้ไขฟังก์ชัน `update_solar_data()` ใน `src/main.cpp` เพื่อเชื่อมต่อกับเซ็นเซอร์จริง:

```cpp
void update_solar_data() {
    // แทนที่ข้อมูลจำลองด้วยการอ่านจากเซ็นเซอร์จริง
    solar_data.voltage = readVoltage();
    solar_data.current = readCurrent();
    solar_data.power = solar_data.voltage * solar_data.current;
    // ...
}
```

### การปรับแต่ง UI

- แก้ไขสีและธีมใน `main.cpp`
- เปลี่ยนขนาดและตำแหน่งของ widgets
- เพิ่ม widgets ใหม่ตามต้องการ

### การตั้งค่า LVGL

แก้ไขไฟล์ `src/lv_conf.h` เพื่อ:
- เปลี่ยนขนาดหน่วยความจำ
- เปิด/ปิด features ต่างๆ
- เปลี่ยน fonts ที่ใช้

## ข้อมูลที่แสดง

1. **Power Arc**: แสดงกำลังไฟฟ้าปัจจุบันเป็น percentage
2. **Voltage Card**: แสดงแรงดันไฟฟ้า (V)
3. **Current Card**: แสดงกระแสไฟฟ้า (A)
4. **Battery Bar**: แสดงระดับแบตเตอรี่ (%)
5. **Status**: แสดงสถานะการชาร์จและพลังงานรวมต่อวัน
6. **Temperature**: แสดงอุณหภูมิของระบบ (°C)

## การแก้ไขปัญหา

### หน้าจอไม่แสดงผล
- ตรวจสอบการเชื่อมต่อขา
- ตรวจสอบแรงดันไฟฟ้า (3.3V)
- ตรวจสอบการตั้งค่าใน `platformio.ini`

### Touch ไม่ทำงาน
- ตรวจสอบการเชื่อมต่อ T_CS (GPIO 21)
- ปรับค่า calibration ใน `setup()`

### หน่วยความจำไม่พอ
- ลดขนาด `LV_MEM_SIZE` ใน `lv_conf.h`
- ปิด features ที่ไม่จำเป็น
- ใช้ fonts ขนาดเล็กลง

## Libraries ที่ใช้

- **LVGL 8.4.0**: Graphics library
- **TFT_eSPI 2.5.0**: Display driver
- **Arduino Framework**: สำหรับ ESP32

## License

MIT License - ใช้งานได้อย่างอิสระ

## การพัฒนาต่อ

- เพิ่มการเชื่อมต่อ WiFi สำหรับ remote monitoring
- เพิ่มการบันทึกข้อมูลลง SD card
- เพิ่มการแจ้งเตือนเมื่อมีปัญหา
- เพิ่มหน้าจอการตั้งค่า
- รองรับ MQTT สำหรับ IoT integration

---

สร้างโดย: Augment Agent
วันที่: 2024
