/*
 * Smart PowMr Data Decoder
 * 
 * This version is smarter about detecting real data vs idle patterns
 */

#include <HardwareSerial.h>

#define RXD2 16
#define TXD2 17

// Function prototypes
void runSmartDecoder();
void analyzeDataStream();
void smartVoltageAnalysis(uint8_t* buffer, int length);
void testSpecificRegisters();

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=== Smart PowMr Data Decoder ===");
  Serial.println("Looking for REAL inverter data...");
  Serial.println("Expected: Battery ~53.8V, Input ~28V\n");
  
  runSmartDecoder();
}

void loop() {
  delay(10000);
}

void runSmartDecoder() {
  Serial2.begin(4800, SERIAL_8N1, RXD2, TXD2);
  delay(1000);
  
  Serial.println("1. Analyzing data stream intelligently...");
  analyzeDataStream();
  
  Serial.println("\n2. Testing specific Modbus registers...");
  testSpecificRegisters();
  
  Serial.println("\n=== Smart Analysis Complete ===");
}

void analyzeDataStream() {
  uint8_t buffer[1024];
  int bufferIndex = 0;
  unsigned long startTime = millis();
  
  Serial.println("   Collecting 20 seconds of data...");
  
  while (millis() - startTime < 20000) {
    if (Serial2.available()) {
      uint8_t data = Serial2.read();
      buffer[bufferIndex] = data;
      bufferIndex++;
      
      if (bufferIndex >= 1024) {
        smartVoltageAnalysis(buffer, bufferIndex);
        bufferIndex = 0;
      }
    }
    delay(1);
  }
  
  if (bufferIndex > 0) {
    smartVoltageAnalysis(buffer, bufferIndex);
  }
}

void smartVoltageAnalysis(uint8_t* buffer, int length) {
  Serial.print("   Analyzing ");
  Serial.print(length);
  Serial.println(" bytes...");
  
  // Show first 16 bytes
  Serial.print("   Raw data: ");
  int showBytes = (length < 16) ? length : 16;
  for (int i = 0; i < showBytes; i++) {
    Serial.print("0x");
    if (buffer[i] < 0x10) Serial.print("0");
    Serial.print(buffer[i], HEX);
    Serial.print(" ");
  }
  Serial.println("...");
  
  // Count unique values
  int uniqueValues[256] = {0};
  for (int i = 0; i < length; i++) {
    uniqueValues[buffer[i]]++;
  }
  
  int uniqueCount = 0;
  for (int i = 0; i < 256; i++) {
    if (uniqueValues[i] > 0) uniqueCount++;
  }
  
  Serial.print("   Unique byte values: ");
  Serial.println(uniqueCount);
  
  // Show most common values
  Serial.print("   Most common: ");
  for (int i = 0; i < 256; i++) {
    if (uniqueValues[i] > length/20) {  // Show values >5% frequency
      Serial.print("0x");
      if (i < 0x10) Serial.print("0");
      Serial.print(i, HEX);
      Serial.print("(");
      Serial.print(uniqueValues[i]);
      Serial.print("x) ");
    }
  }
  Serial.println();
  
  if (uniqueCount <= 4) {
    Serial.println("   >>> IDLE PATTERN DETECTED - Skipping voltage analysis");
    return;
  }
  
  // Look for realistic voltage values
  int potentialFinds = 0;
  for (int i = 0; i < length - 1; i++) {
    uint16_t value16_be = (buffer[i] << 8) | buffer[i + 1];
    uint16_t value16_le = (buffer[i + 1] << 8) | buffer[i];
    
    // Skip obvious patterns
    if (value16_be == 0x00FF || value16_be == 0xFF00 || 
        value16_be == 0x0000 || value16_be == 0xFFFF) continue;
    if (value16_le == 0x00FF || value16_le == 0xFF00 || 
        value16_le == 0x0000 || value16_le == 0xFFFF) continue;
    
    // Test realistic scaling factors
    float scales[] = {0.01f, 0.1f, 1.0f, 10.0f, 100.0f};
    const char* scaleNames[] = {"0.01", "0.1", "1.0", "10", "100"};
    
    for (int s = 0; s < 5; s++) {
      float volt_be = value16_be / scales[s];
      float volt_le = value16_le / scales[s];
      
      // Check for battery voltage (53.8V ±8V)
      if (volt_be >= 45.8 && volt_be <= 61.8) {
        Serial.print("   *** BATTERY? *** Pos ");
        Serial.print(i);
        Serial.print(": 0x");
        Serial.print(value16_be, HEX);
        Serial.print(" = ");
        Serial.print(volt_be, 2);
        Serial.print("V (BE, /");
        Serial.print(scaleNames[s]);
        Serial.println(")");
        potentialFinds++;
      }
      
      if (volt_le >= 45.8 && volt_le <= 61.8) {
        Serial.print("   *** BATTERY? *** Pos ");
        Serial.print(i);
        Serial.print(": 0x");
        Serial.print(value16_le, HEX);
        Serial.print(" = ");
        Serial.print(volt_le, 2);
        Serial.print("V (LE, /");
        Serial.print(scaleNames[s]);
        Serial.println(")");
        potentialFinds++;
      }
      
      // Check for input voltage (28V ±12V)
      if (volt_be >= 16 && volt_be <= 40) {
        Serial.print("   *** INPUT? *** Pos ");
        Serial.print(i);
        Serial.print(": 0x");
        Serial.print(value16_be, HEX);
        Serial.print(" = ");
        Serial.print(volt_be, 2);
        Serial.print("V (BE, /");
        Serial.print(scaleNames[s]);
        Serial.println(")");
        potentialFinds++;
      }
      
      if (volt_le >= 16 && volt_le <= 40) {
        Serial.print("   *** INPUT? *** Pos ");
        Serial.print(i);
        Serial.print(": 0x");
        Serial.print(value16_le, HEX);
        Serial.print(" = ");
        Serial.print(volt_le, 2);
        Serial.print("V (LE, /");
        Serial.print(scaleNames[s]);
        Serial.println(")");
        potentialFinds++;
      }
    }
  }
  
  if (potentialFinds == 0) {
    Serial.println("   >>> NO REALISTIC VOLTAGES FOUND");
  } else {
    Serial.print("   >>> Found ");
    Serial.print(potentialFinds);
    Serial.println(" potential voltage readings!");
  }
}

void testSpecificRegisters() {
  // Test specific register addresses that commonly contain voltage data
  uint16_t testRegisters[] = {
    0x0000, 0x0001, 0x0002, 0x0003, 0x0004, 0x0005,
    0x0010, 0x0011, 0x0012, 0x0013, 0x0014, 0x0015,
    0x0020, 0x0021, 0x0022, 0x0023, 0x0024, 0x0025,
    0x0100, 0x0101, 0x0102, 0x0103, 0x0104, 0x0105
  };
  
  for (int regIdx = 0; regIdx < 24; regIdx++) {
    uint16_t regAddr = testRegisters[regIdx];
    
    // Build Modbus frame: Function 04 (Read Input Registers)
    uint8_t frame[8];
    frame[0] = 0x01;  // Slave ID
    frame[1] = 0x04;  // Function 04
    frame[2] = (regAddr >> 8) & 0xFF;
    frame[3] = regAddr & 0xFF;
    frame[4] = 0x00;  // Quantity high
    frame[5] = 0x01;  // Quantity low (read 1 register)
    
    // Simple CRC
    uint16_t crc = 0xFFFF;
    for (int i = 0; i < 6; i++) {
      crc ^= frame[i];
      for (int j = 0; j < 8; j++) {
        if (crc & 0x0001) {
          crc = (crc >> 1) ^ 0xA001;
        } else {
          crc = crc >> 1;
        }
      }
    }
    frame[6] = crc & 0xFF;
    frame[7] = (crc >> 8) & 0xFF;
    
    // Clear buffer and send
    while (Serial2.available()) Serial2.read();
    
    for (int i = 0; i < 8; i++) {
      Serial2.write(frame[i]);
    }
    
    delay(200);
    
    // Read response
    if (Serial2.available()) {
      uint8_t response[10];
      int respLen = 0;
      
      while (Serial2.available() && respLen < 10) {
        response[respLen] = Serial2.read();
        respLen++;
      }
      
      // Check for valid Modbus response
      if (respLen >= 5 && response[0] == 0x01 && response[1] == 0x04) {
        uint8_t byteCount = response[2];
        if (byteCount == 2 && respLen >= 5) {
          uint16_t regValue = (response[3] << 8) | response[4];
          
          Serial.print("   Reg 0x");
          Serial.print(regAddr, HEX);
          Serial.print(": 0x");
          Serial.print(regValue, HEX);
          Serial.print(" = ");
          
          // Test different scales
          float scales[] = {0.01f, 0.1f, 1.0f, 10.0f, 100.0f};
          for (int s = 0; s < 5; s++) {
            float voltage = regValue / scales[s];
            if ((voltage >= 45 && voltage <= 65) || (voltage >= 15 && voltage <= 45)) {
              Serial.print(voltage, 2);
              Serial.print("V(");
              Serial.print(scales[s], 2);
              Serial.print(") ");
            }
          }
          Serial.println();
        }
      }
    }
    
    delay(100);
  }
}
