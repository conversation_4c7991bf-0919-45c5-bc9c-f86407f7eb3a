/*
 * PowMr Inverter Test Suite
 * 
 * This file contains test functions to verify the communication
 * and functionality with PowMr inverters.
 */

#include <HardwareSerial.h>
#include <ModbusRTU.h>

// Test configuration
#define TEST_SLAVE_ID 5
#define TEST_BAUD_RATE 2400
#define RXD2 16
#define TXD2 17

ModbusRTU mb;

// Test results structure
struct TestResult {
    bool passed;
    String description;
    String details;
};

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("=== PowMr Inverter Test Suite ===");
    Serial.println("Starting comprehensive tests...\n");
    
    // Initialize Modbus
    Serial2.begin(TEST_BAUD_RATE, SERIAL_8N1, RXD2, TXD2);
    mb.begin(&Serial2);
    delay(1000);
    
    // Run all tests
    runAllTests();
}

void loop() {
    // Test suite runs once in setup
    delay(10000);
}

void runAllTests() {
    TestResult results[10];
    int testCount = 0;
    
    // Communication tests
    results[testCount++] = testBasicCommunication();
    results[testCount++] = testSlaveIdDetection();
    results[testCount++] = testBaudRateDetection();
    
    // Data reading tests
    results[testCount++] = testStatusRegisters();
    results[testCount++] = testExtendedRegisters();
    results[testCount++] = testDataValidation();
    
    // Control tests (safe operations only)
    results[testCount++] = testBuzzerControl();
    results[testCount++] = testReadOnlyRegisters();
    
    // Performance tests
    results[testCount++] = testReadSpeed();
    results[testCount++] = testConnectionStability();
    
    // Print test summary
    printTestSummary(results, testCount);
}

TestResult testBasicCommunication() {
    TestResult result;
    result.description = "Basic Communication Test";
    
    uint16_t testData[5];
    bool success = mb.readHreg(TEST_SLAVE_ID, 4501, testData, 5, nullptr);
    
    if (success) {
        result.passed = true;
        result.details = "Successfully read 5 registers from address 4501";
    } else {
        result.passed = false;
        result.details = "Failed to read registers - check wiring and settings";
    }
    
    return result;
}

TestResult testSlaveIdDetection() {
    TestResult result;
    result.description = "Slave ID Detection Test";
    
    uint8_t testIds[] = {1, 5, 10, 247};
    bool foundId = false;
    uint8_t workingId = 0;
    
    for (int i = 0; i < 4; i++) {
        uint16_t testData[3];
        if (mb.readHreg(testIds[i], 4501, testData, 3, nullptr)) {
            foundId = true;
            workingId = testIds[i];
            break;
        }
        delay(500);
    }
    
    if (foundId) {
        result.passed = true;
        result.details = "Found working Slave ID: " + String(workingId);
    } else {
        result.passed = false;
        result.details = "No responsive Slave ID found";
    }
    
    return result;
}

TestResult testBaudRateDetection() {
    TestResult result;
    result.description = "Baud Rate Detection Test";
    
    uint32_t testRates[] = {2400, 9600, 4800, 19200};
    bool foundRate = false;
    uint32_t workingRate = 0;
    
    for (int i = 0; i < 4; i++) {
        Serial2.end();
        delay(100);
        Serial2.begin(testRates[i], SERIAL_8N1, RXD2, TXD2);
        mb.begin(&Serial2);
        delay(500);
        
        uint16_t testData[3];
        if (mb.readHreg(TEST_SLAVE_ID, 4501, testData, 3, nullptr)) {
            foundRate = true;
            workingRate = testRates[i];
            break;
        }
    }
    
    if (foundRate) {
        result.passed = true;
        result.details = "Found working Baud Rate: " + String(workingRate);
    } else {
        result.passed = false;
        result.details = "No responsive Baud Rate found";
        // Restore original baud rate
        Serial2.end();
        Serial2.begin(TEST_BAUD_RATE, SERIAL_8N1, RXD2, TXD2);
        mb.begin(&Serial2);
    }
    
    return result;
}

TestResult testStatusRegisters() {
    TestResult result;
    result.description = "Status Registers Test";
    
    uint16_t statusData[45];
    bool success = mb.readHreg(TEST_SLAVE_ID, 4501, statusData, 45, nullptr);
    
    if (success) {
        // Validate data ranges
        float batteryVoltage = statusData[0] * 0.01;
        uint16_t batteryCapacity = statusData[2];
        
        if (batteryVoltage > 5.0 && batteryVoltage < 70.0 && 
            batteryCapacity <= 100) {
            result.passed = true;
            result.details = "All 45 status registers read successfully. Battery: " + 
                           String(batteryVoltage, 2) + "V, " + String(batteryCapacity) + "%";
        } else {
            result.passed = false;
            result.details = "Data validation failed - unusual values detected";
        }
    } else {
        result.passed = false;
        result.details = "Failed to read status registers";
    }
    
    return result;
}

TestResult testExtendedRegisters() {
    TestResult result;
    result.description = "Extended Registers Test";
    
    uint16_t extendedData[16];
    bool success = mb.readHreg(TEST_SLAVE_ID, 4546, extendedData, 16, nullptr);
    
    if (success) {
        result.passed = true;
        result.details = "Successfully read 16 extended registers from address 4546";
    } else {
        result.passed = false;
        result.details = "Failed to read extended registers";
    }
    
    return result;
}

TestResult testDataValidation() {
    TestResult result;
    result.description = "Data Validation Test";
    
    uint16_t data[20];
    bool success = mb.readHreg(TEST_SLAVE_ID, 4501, data, 20, nullptr);
    
    if (success) {
        int invalidCount = 0;
        for (int i = 0; i < 20; i++) {
            if (data[i] > 65000) {
                invalidCount++;
            }
        }
        
        if (invalidCount == 0) {
            result.passed = true;
            result.details = "All data values are within valid range";
        } else {
            result.passed = false;
            result.details = "Found " + String(invalidCount) + " invalid data values";
        }
    } else {
        result.passed = false;
        result.details = "Failed to read data for validation";
    }
    
    return result;
}

TestResult testBuzzerControl() {
    TestResult result;
    result.description = "Buzzer Control Test";
    
    // Read current buzzer state
    uint16_t originalState;
    bool readSuccess = mb.readHreg(TEST_SLAVE_ID, 5002, &originalState, 1, nullptr);
    
    if (readSuccess) {
        // Try to write the same value back (safe operation)
        bool writeSuccess = mb.writeHreg(TEST_SLAVE_ID, 5002, originalState, nullptr);
        
        if (writeSuccess) {
            result.passed = true;
            result.details = "Buzzer control register accessible (current state: " + 
                           String(originalState) + ")";
        } else {
            result.passed = false;
            result.details = "Failed to write to buzzer control register";
        }
    } else {
        result.passed = false;
        result.details = "Failed to read buzzer control register";
    }
    
    return result;
}

TestResult testReadOnlyRegisters() {
    TestResult result;
    result.description = "Read-Only Registers Test";
    
    // Test that we cannot write to status registers
    uint16_t originalValue;
    bool readSuccess = mb.readHreg(TEST_SLAVE_ID, 4501, &originalValue, 1, nullptr);
    
    if (readSuccess) {
        // Try to write to a read-only register (should fail)
        bool writeSuccess = mb.writeHreg(TEST_SLAVE_ID, 4501, originalValue + 1, nullptr);
        
        if (!writeSuccess) {
            result.passed = true;
            result.details = "Read-only registers properly protected";
        } else {
            result.passed = false;
            result.details = "Warning: Was able to write to read-only register";
        }
    } else {
        result.passed = false;
        result.details = "Failed to read register for testing";
    }
    
    return result;
}

TestResult testReadSpeed() {
    TestResult result;
    result.description = "Read Speed Test";
    
    unsigned long startTime = millis();
    int successfulReads = 0;
    
    for (int i = 0; i < 10; i++) {
        uint16_t data[10];
        if (mb.readHreg(TEST_SLAVE_ID, 4501, data, 10, nullptr)) {
            successfulReads++;
        }
        delay(100);
    }
    
    unsigned long totalTime = millis() - startTime;
    
    if (successfulReads >= 8) {
        result.passed = true;
        result.details = String(successfulReads) + "/10 reads successful in " + 
                        String(totalTime) + "ms";
    } else {
        result.passed = false;
        result.details = "Only " + String(successfulReads) + "/10 reads successful";
    }
    
    return result;
}

TestResult testConnectionStability() {
    TestResult result;
    result.description = "Connection Stability Test";
    
    int consecutiveFailures = 0;
    int maxFailures = 0;
    int totalReads = 20;
    int successfulReads = 0;
    
    for (int i = 0; i < totalReads; i++) {
        uint16_t data[5];
        if (mb.readHreg(TEST_SLAVE_ID, 4501, data, 5, nullptr)) {
            consecutiveFailures = 0;
            successfulReads++;
        } else {
            consecutiveFailures++;
            if (consecutiveFailures > maxFailures) {
                maxFailures = consecutiveFailures;
            }
        }
        delay(250);
    }
    
    float successRate = (float)successfulReads / totalReads * 100;
    
    if (successRate >= 90 && maxFailures <= 2) {
        result.passed = true;
        result.details = String(successRate, 1) + "% success rate, max consecutive failures: " + 
                        String(maxFailures);
    } else {
        result.passed = false;
        result.details = "Poor stability: " + String(successRate, 1) + 
                        "% success rate, max consecutive failures: " + String(maxFailures);
    }
    
    return result;
}

void printTestSummary(TestResult* results, int count) {
    Serial.println("\n=== Test Summary ===");
    
    int passed = 0;
    for (int i = 0; i < count; i++) {
        Serial.print(results[i].passed ? "✓ PASS" : "✗ FAIL");
        Serial.print(" - ");
        Serial.println(results[i].description);
        Serial.print("      ");
        Serial.println(results[i].details);
        Serial.println();
        
        if (results[i].passed) passed++;
    }
    
    Serial.println("==================");
    Serial.print("Tests passed: ");
    Serial.print(passed);
    Serial.print("/");
    Serial.println(count);
    
    if (passed == count) {
        Serial.println("🎉 All tests passed! Inverter is fully compatible.");
    } else if (passed >= count * 0.8) {
        Serial.println("⚠ Most tests passed. Minor issues detected.");
    } else {
        Serial.println("❌ Multiple test failures. Check configuration and wiring.");
    }
}
