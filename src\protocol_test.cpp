/*
 * PowMr Protocol Detection Test
 * 
 * Since we detected data at 4800 baud but no Modbus response,
 * this test will try different protocols and data formats.
 */

#include <HardwareSerial.h>

#define RXD2 16
#define TXD2 17

// Function prototypes
void runProtocolTests();
void testDataCapture();
void testDifferentProtocols();
void analyzeReceivedData(uint8_t* data, int length);

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=== PowMr Protocol Detection Test ===");
  Serial.println("We detected data at 4800 baud rate.");
  Serial.println("Testing different protocols and data formats...\n");
  
  runProtocolTests();
}

void loop() {
  // Test runs once in setup
  delay(10000);
}

void runProtocolTests() {
  Serial.println("1. Capturing Raw Data Stream...");
  testDataCapture();
  
  Serial.println("\n2. Testing Different Protocol Commands...");
  testDifferentProtocols();
  
  Serial.println("\n=== Protocol Test Complete ===");
}

void testDataCapture() {
  // Initialize with the baud rate that showed data
  Serial2.begin(4800, SERIAL_8N1, RXD2, TXD2);
  delay(1000);
  
  Serial.println("   Capturing data for 15 seconds at 4800 baud...");
  
  uint8_t buffer[256];
  int bufferIndex = 0;
  unsigned long startTime = millis();
  unsigned long lastDataTime = 0;
  
  while (millis() - startTime < 15000) {
    if (Serial2.available()) {
      uint8_t data = Serial2.read();
      buffer[bufferIndex] = data;
      bufferIndex++;
      lastDataTime = millis();
      
      // Print data in real-time
      Serial.print("0x");
      if (data < 0x10) Serial.print("0");
      Serial.print(data, HEX);
      Serial.print(" ");
      
      // Print ASCII if printable
      if (data >= 32 && data <= 126) {
        Serial.print("(");
        Serial.print((char)data);
        Serial.print(") ");
      }
      
      // New line every 16 bytes
      if (bufferIndex % 16 == 0) {
        Serial.println();
      }
      
      // Analyze when buffer is full or no more data for 2 seconds
      if (bufferIndex >= 255 || (bufferIndex > 0 && millis() - lastDataTime > 2000)) {
        Serial.println("\n   --- Analyzing captured data ---");
        analyzeReceivedData(buffer, bufferIndex);
        bufferIndex = 0;
        Serial.println("   --- Continuing capture ---");
      }
    }
    delay(10);
  }
  
  if (bufferIndex > 0) {
    Serial.println("\n   --- Final data analysis ---");
    analyzeReceivedData(buffer, bufferIndex);
  }
  
  Serial2.end();
}

void testDifferentProtocols() {
  Serial2.begin(4800, SERIAL_8N1, RXD2, TXD2);
  delay(1000);
  
  // Test 1: ASCII-based protocol
  Serial.println("   Testing ASCII commands...");
  Serial2.print("STATUS\r\n");
  delay(1000);
  if (Serial2.available()) {
    Serial.print("   ASCII Response: ");
    while (Serial2.available()) {
      char c = Serial2.read();
      Serial.print(c);
    }
    Serial.println();
  } else {
    Serial.println("   No ASCII response");
  }
  
  // Test 2: Simple binary commands
  Serial.println("   Testing binary commands...");
  uint8_t binaryCommands[] = {0x01, 0x02, 0x03, 0x04, 0x05, 0xAA, 0x55, 0xFF};
  
  for (int i = 0; i < 8; i++) {
    // Clear buffer
    while (Serial2.available()) Serial2.read();
    
    Serial.print("   Sending: 0x");
    Serial.println(binaryCommands[i], HEX);
    Serial2.write(binaryCommands[i]);
    delay(500);
    
    if (Serial2.available()) {
      Serial.print("   Response: ");
      while (Serial2.available()) {
        uint8_t data = Serial2.read();
        Serial.print("0x");
        if (data < 0x10) Serial.print("0");
        Serial.print(data, HEX);
        Serial.print(" ");
      }
      Serial.println();
    }
  }
  
  // Test 3: Different Modbus-like protocols
  Serial.println("   Testing Modbus variations...");
  
  // RTU with different function codes
  uint8_t modbusVariations[][8] = {
    {0x01, 0x04, 0x00, 0x00, 0x00, 0x01, 0x31, 0xCA}, // Read Input Registers
    {0x05, 0x04, 0x00, 0x00, 0x00, 0x01, 0x31, 0x9A}, // Slave 5
    {0x01, 0x01, 0x00, 0x00, 0x00, 0x01, 0xFD, 0xCA}, // Read Coils
    {0x01, 0x02, 0x00, 0x00, 0x00, 0x01, 0xF9, 0xCA}, // Read Discrete Inputs
  };
  
  for (int i = 0; i < 4; i++) {
    // Clear buffer
    while (Serial2.available()) Serial2.read();
    
    Serial.print("   Modbus variation ");
    Serial.print(i + 1);
    Serial.print(": ");
    for (int j = 0; j < 8; j++) {
      Serial.print("0x");
      if (modbusVariations[i][j] < 0x10) Serial.print("0");
      Serial.print(modbusVariations[i][j], HEX);
      Serial.print(" ");
      Serial2.write(modbusVariations[i][j]);
    }
    Serial.println();
    
    delay(1000);
    
    if (Serial2.available()) {
      Serial.print("   Response: ");
      while (Serial2.available()) {
        uint8_t data = Serial2.read();
        Serial.print("0x");
        if (data < 0x10) Serial.print("0");
        Serial.print(data, HEX);
        Serial.print(" ");
      }
      Serial.println();
    } else {
      Serial.println("   No response");
    }
  }
  
  Serial2.end();
}

void analyzeReceivedData(uint8_t* data, int length) {
  if (length == 0) {
    Serial.println("   No data to analyze");
    return;
  }
  
  Serial.print("   Data length: ");
  Serial.println(length);
  
  // Check for patterns
  bool allSame = true;
  bool allFF = true;
  bool all00 = true;
  
  for (int i = 1; i < length; i++) {
    if (data[i] != data[0]) allSame = false;
    if (data[i] != 0xFF) allFF = false;
    if (data[i] != 0x00) all00 = false;
  }
  
  if (allFF) {
    Serial.println("   Pattern: All 0xFF (possible noise or idle state)");
  } else if (all00) {
    Serial.println("   Pattern: All 0x00 (possible idle state)");
  } else if (allSame) {
    Serial.print("   Pattern: All same value 0x");
    Serial.println(data[0], HEX);
  } else {
    Serial.println("   Pattern: Variable data (possible real communication)");
    
    // Look for potential voltage values
    for (int i = 0; i < length - 1; i++) {
      uint16_t value = (data[i] << 8) | data[i + 1];
      float voltage1 = value * 0.1;
      float voltage2 = value * 0.01;
      
      // Check for battery voltage pattern (53.8V ±3V)
      if ((voltage1 >= 50.8 && voltage1 <= 56.8) || 
          (voltage2 >= 50.8 && voltage2 <= 56.8)) {
        Serial.print("   *** POTENTIAL BATTERY VOLTAGE at bytes ");
        Serial.print(i);
        Serial.print("-");
        Serial.print(i + 1);
        Serial.print(": 0x");
        Serial.print(value, HEX);
        Serial.print(" = ");
        Serial.print(voltage1, 1);
        Serial.print("V or ");
        Serial.print(voltage2, 2);
        Serial.println("V ***");
      }
      
      // Check for input voltage pattern (28V ±7V)
      if ((voltage1 >= 21 && voltage1 <= 35) || 
          (voltage2 >= 21 && voltage2 <= 35)) {
        Serial.print("   *** POTENTIAL INPUT VOLTAGE at bytes ");
        Serial.print(i);
        Serial.print("-");
        Serial.print(i + 1);
        Serial.print(": 0x");
        Serial.print(value, HEX);
        Serial.print(" = ");
        Serial.print(voltage1, 1);
        Serial.print("V or ");
        Serial.print(voltage2, 2);
        Serial.println("V ***");
      }
    }
  }
  
  // Check for ASCII content
  bool hasASCII = false;
  for (int i = 0; i < length; i++) {
    if (data[i] >= 32 && data[i] <= 126) {
      hasASCII = true;
      break;
    }
  }
  
  if (hasASCII) {
    Serial.print("   ASCII content: ");
    for (int i = 0; i < length; i++) {
      if (data[i] >= 32 && data[i] <= 126) {
        Serial.print((char)data[i]);
      } else {
        Serial.print(".");
      }
    }
    Serial.println();
  }
}
