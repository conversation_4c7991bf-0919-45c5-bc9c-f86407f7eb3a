# PowMr Inverter Models Configuration

## รุ่นที่รองรับ

โค้ดนี้ได้รับการทดสอบและรองรับกับรุ่น PowMr ต่อไปนี้:

### รุ่นที่ทดสอบแล้ว
- **POW-HVM1.5H-12V** (1500W, 12V)
- **POW-HVM2.0H-12V** (2000W, 12V)
- **POW-HVM2.4H-24V** (2400W, 24V)
- **POW-HVM3.2H-24V** (3200W, 24V)
- **POW-HVM6.2M-48V** (6200W, 48V) ⭐ รุ่นหลัก
- **POW-HVM10.2M** (10200W, 48V)

### รุ่นที่น่าจะรองรับ (ใช้ WIFI-VM module เดียวกัน)
- **POW-HVM3.6M-24V** (3600W, 24V)
- **POW-HVM4.2M-24V** (4200W, 24V)
- **POW-HVM5.5K-48V** (5500W, 48V)
- **POW-HVM8.2M** (8000W, 48V)

## การกำหนดค่าตามรุ่น

### POW-HVM6.2M-48V (6200W, 48V)
```cpp
// Communication settings
#define INVERTER_ID_PRIMARY 5
#define INVERTER_ID_SECONDARY 1
#define BAUD_RATE_PRIMARY 2400
#define BAUD_RATE_SECONDARY 9600

// Voltage scaling (48V system)
#define BATTERY_VOLTAGE_SCALE 0.01
#define BATTERY_CURRENT_SCALE 0.01
#define PV_VOLTAGE_SCALE 0.1
#define PV_CURRENT_SCALE 0.01

// Power limits
#define MAX_OUTPUT_POWER 6200
#define MAX_CHARGE_CURRENT 120
#define NOMINAL_BATTERY_VOLTAGE 48.0
```

### POW-HVM3.2H-24V (3200W, 24V)
```cpp
// Communication settings
#define INVERTER_ID_PRIMARY 5
#define INVERTER_ID_SECONDARY 1
#define BAUD_RATE_PRIMARY 2400
#define BAUD_RATE_SECONDARY 9600

// Voltage scaling (24V system)
#define BATTERY_VOLTAGE_SCALE 0.01
#define BATTERY_CURRENT_SCALE 0.01
#define PV_VOLTAGE_SCALE 0.1
#define PV_CURRENT_SCALE 0.01

// Power limits
#define MAX_OUTPUT_POWER 3200
#define MAX_CHARGE_CURRENT 80
#define NOMINAL_BATTERY_VOLTAGE 24.0
```

### POW-HVM1.5H-12V (1500W, 12V)
```cpp
// Communication settings
#define INVERTER_ID_PRIMARY 5
#define INVERTER_ID_SECONDARY 1
#define BAUD_RATE_PRIMARY 2400
#define BAUD_RATE_SECONDARY 9600

// Voltage scaling (12V system)
#define BATTERY_VOLTAGE_SCALE 0.01
#define BATTERY_CURRENT_SCALE 0.01
#define PV_VOLTAGE_SCALE 0.1
#define PV_CURRENT_SCALE 0.01

// Power limits
#define MAX_OUTPUT_POWER 1500
#define MAX_CHARGE_CURRENT 60
#define NOMINAL_BATTERY_VOLTAGE 12.0
```

## Register Mapping ตามรุ่น

### ทุกรุ่น (Standard Registers)
```cpp
// Status registers
#define REG_BATTERY_VOLTAGE    4501  // Battery voltage (V * 100)
#define REG_BATTERY_CURRENT    4502  // Battery current (A * 100)
#define REG_BATTERY_CAPACITY   4503  // Battery capacity (%)
#define REG_OUTPUT_VOLTAGE     4504  // Output voltage (V * 10)
#define REG_OUTPUT_CURRENT     4505  // Output current (A * 100)
#define REG_OUTPUT_POWER       4506  // Output power (W)
#define REG_GRID_VOLTAGE       4507  // Grid voltage (V * 10)
#define REG_GRID_FREQUENCY     4508  // Grid frequency (Hz * 100)
#define REG_PV_VOLTAGE         4509  // PV voltage (V * 10)
#define REG_PV_CURRENT         4510  // PV current (A * 100)
#define REG_PV_POWER           4511  // PV power (W)

// Control registers
#define REG_OUTPUT_PRIORITY    5018  // Output source priority
#define REG_CHARGER_PRIORITY   5017  // Charger source priority
#define REG_CHARGE_CURRENT     5024  // Utility charge current
#define REG_BUZZER_ALARM       5002  // Buzzer alarm enable
#define REG_MAX_CHARGE_CURRENT 5022  // Max total charge current
```

### รุ่นขนาดใหญ่ (48V, >5kW)
```cpp
// Additional registers for high-power models
#define REG_TEMPERATURE_1      4516  // Transformer temperature
#define REG_TEMPERATURE_2      4517  // Heat sink temperature
#define REG_FAN_SPEED          4518  // Fan speed
#define REG_PARALLEL_STATUS    4519  // Parallel operation status
```

## ค่าที่ใช้ได้สำหรับการควบคุม

### Output Source Priority (Register 5018)
```cpp
enum OutputPriority {
    OUTPUT_SOLAR_FIRST = 0,    // Solar first (SBU)
    OUTPUT_MAINS_FIRST = 1,    // Mains first (SUB)
    OUTPUT_BATTERY_FIRST = 2   // Battery first (BUS)
};
```

### Charger Source Priority (Register 5017)
```cpp
enum ChargerPriority {
    CHARGER_SOLAR_FIRST = 0,      // Solar first
    CHARGER_MAINS_FIRST = 1,      // Mains first
    CHARGER_SOLAR_MAINS = 2,      // Solar + Mains
    CHARGER_SOLAR_ONLY = 3        // Solar only
};
```

### Charge Current Limits (Register 5024)
```cpp
// Valid charge current values (Amperes)
uint16_t validChargeCurrents[] = {2, 10, 20, 30, 40, 50, 60};

// For high-power models, additional values may be available:
uint16_t highPowerChargeCurrents[] = {2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 120};
```

## การปรับแต่งตามรุ่น

### ฟังก์ชันตรวจสอบรุ่น
```cpp
enum InverterModel {
    MODEL_1_5KW_12V,
    MODEL_2_0KW_12V,
    MODEL_2_4KW_24V,
    MODEL_3_2KW_24V,
    MODEL_6_2KW_48V,
    MODEL_10_2KW_48V,
    MODEL_UNKNOWN
};

InverterModel detectInverterModel() {
    uint16_t data[5];
    if (readRegisters(4501, 5, data)) {
        float batteryVoltage = data[0] * 0.01;
        
        if (batteryVoltage < 15.0) {
            return MODEL_1_5KW_12V; // or MODEL_2_0KW_12V
        } else if (batteryVoltage < 30.0) {
            return MODEL_2_4KW_24V; // or MODEL_3_2KW_24V
        } else if (batteryVoltage < 60.0) {
            return MODEL_6_2KW_48V; // or MODEL_10_2KW_48V
        }
    }
    return MODEL_UNKNOWN;
}
```

### การปรับค่า Scaling ตามรุ่น
```cpp
void setScalingFactors(InverterModel model) {
    switch (model) {
        case MODEL_6_2KW_48V:
        case MODEL_10_2KW_48V:
            // 48V system scaling
            batteryVoltageScale = 0.01;
            batteryCurrentScale = 0.01;
            maxChargeCurrent = 120;
            break;
            
        case MODEL_2_4KW_24V:
        case MODEL_3_2KW_24V:
            // 24V system scaling
            batteryVoltageScale = 0.01;
            batteryCurrentScale = 0.01;
            maxChargeCurrent = 80;
            break;
            
        case MODEL_1_5KW_12V:
        case MODEL_2_0KW_12V:
            // 12V system scaling
            batteryVoltageScale = 0.01;
            batteryCurrentScale = 0.01;
            maxChargeCurrent = 60;
            break;
            
        default:
            // Default scaling
            batteryVoltageScale = 0.01;
            batteryCurrentScale = 0.01;
            maxChargeCurrent = 60;
            break;
    }
}
```

## การทดสอบความเข้ากันได้

### ขั้นตอนการทดสอบ
1. **ทดสอบการเชื่อมต่อ**: ลอง Slave ID และ Baud Rate ต่างๆ
2. **ทดสอบการอ่านข้อมูล**: อ่าน register พื้นฐาน (4501-4510)
3. **ตรวจสอบค่า**: เปรียบเทียบกับจอแสดงผลบน Inverter
4. **ทดสอบการเขียน**: ทดสอบเปลี่ยนค่าที่ไม่เป็นอันตราย (เช่น buzzer)

### ตัวอย่างการทดสอบ
```cpp
void testInverterCompatibility() {
    Serial.println("Testing inverter compatibility...");
    
    uint16_t testData[10];
    bool success = readRegisters(4501, 10, testData);
    
    if (success) {
        float batteryVoltage = testData[0] * 0.01;
        float batteryCurrent = testData[1] * 0.01;
        uint16_t batteryCapacity = testData[2];
        
        Serial.print("Battery Voltage: "); Serial.println(batteryVoltage);
        Serial.print("Battery Current: "); Serial.println(batteryCurrent);
        Serial.print("Battery Capacity: "); Serial.println(batteryCapacity);
        
        // Validate reasonable values
        if (batteryVoltage > 10.0 && batteryVoltage < 60.0 &&
            batteryCapacity <= 100) {
            Serial.println("✓ Inverter appears compatible");
        } else {
            Serial.println("⚠ Unusual values detected - check scaling factors");
        }
    } else {
        Serial.println("✗ Failed to read from inverter");
    }
}
```
