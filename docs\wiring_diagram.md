# PowMr Inverter Wiring Diagram

## การเชื่อมต่อ ESP32 กับ PowMr Inverter

### ภาพรวมการเชื่อมต่อ

```
PowMr Inverter (RJ45)    RS232 Converter    ESP32
┌─────────────────┐      ┌─────────────┐    ┌──────────────┐
│ Pin 1 (W/O) TX  │────→ │ RX          │    │              │
│ Pin 2 (O)   RX  │←──── │ TX          │    │              │
│ Pin 4 (B)   +12V│────→ │ VCC         │    │              │
│ Pin 8 (Br)  GND │────→ │ GND         │    │              │
└─────────────────┘      │             │    │              │
                         │ TTL_TX      │────→│ GPIO16 (RX2) │
                         │ TTL_RX      │←──── │ GPIO17 (TX2) │
                         │ TTL_VCC     │────→ │ 3.3V         │
                         │ TTL_GND     │────→ │ GND          │
                         └─────────────┘    └──────────────┘
```

### รายละเอียดการเชื่อมต่อ

#### PowMr Inverter RJ45 Connector
```
   1 2 3 4 5 6 7 8
   ┌─┬─┬─┬─┬─┬─┬─┬─┐
   │W│O│ │B│ │ │ │B│
   │/│r│ │l│ │ │ │r│
   │O│a│ │u│ │ │ │o│
   │ │n│ │e│ │ │ │w│
   │ │g│ │ │ │ │ │n│
   │ │e│ │ │ │ │ │ │
   └─┴─┴─┴─┴─┴─┴─┴─┘
```

- **Pin 1 (White/Orange)**: TX - ข้อมูลจาก Inverter
- **Pin 2 (Orange)**: RX - ข้อมูลไป Inverter  
- **Pin 4 (Blue)**: +12V - แหล่งจ่ายไฟ
- **Pin 8 (Brown)**: GND - กราวด์

#### RS232 Level Converter (MAX3232/SP3232)
```
RS232 Side          TTL Side
┌─────────────┐    ┌─────────────┐
│ RX (Pin 2)  │    │ TTL_TX      │
│ TX (Pin 3)  │    │ TTL_RX      │
│ GND (Pin 5) │    │ TTL_GND     │
│ VCC (+12V)  │    │ TTL_VCC     │
└─────────────┘    └─────────────┘
```

#### ESP32 Connections
```
ESP32 Pin    Function    Connection
─────────────────────────────────────
GPIO16       RX2         TTL_TX from converter
GPIO17       TX2         TTL_RX to converter
3.3V         Power       TTL_VCC to converter
GND          Ground      TTL_GND to converter
```

### Schematic Diagram

```
                    ┌─────────────────────────────────────┐
                    │           PowMr Inverter            │
                    │                                     │
                    │  RJ45 Connector                     │
                    │  ┌─┬─┬─┬─┬─┬─┬─┬─┐                  │
                    │  │1│2│3│4│5│6│7│8│                  │
                    │  └┬┴┬┴─┴┬┴─┴─┴─┴┬┘                  │
                    └───┼─┼───┼───────┼───────────────────┘
                        │ │   │       │
                     TX │ │RX │+12V   │GND
                        │ │   │       │
              ┌─────────┼─┼───┼───────┼─────────┐
              │         │ │   │       │         │
              │    ┌────▼─▼───▼───────▼────┐    │
              │    │     MAX3232/SP3232     │    │
              │    │   RS232 Converter      │    │
              │    │                        │    │
              │    │ RS232    │    TTL      │    │
              │    │  Side    │    Side     │    │
              │    └──────────┼─────────────┘    │
              │               │                  │
              └───────────────┼──────────────────┘
                              │
                    ┌─────────┼─────────┐
                    │         │         │
                    │    ┌────▼────┐    │
                    │    │  ESP32  │    │
                    │    │         │    │
                    │    │ GPIO16  │◄── TTL_TX
                    │    │ GPIO17  │──► TTL_RX
                    │    │  3.3V   │──► TTL_VCC
                    │    │  GND    │──► TTL_GND
                    │    └─────────┘    │
                    │                   │
                    └───────────────────┘
```

### ข้อควรระวัง

1. **ระดับสัญญาณ**: PowMr ใช้ RS232 (+/-12V) ต้องใช้ level converter
2. **การ Grounding**: ต้องเชื่อมต่อ GND ร่วมกันทุกจุด
3. **RF Noise**: อาจต้องเพิ่ม capacitor ที่ RX pin เพื่อกรอง noise
4. **Power Supply**: สามารถใช้ +12V จาก Inverter หรือจ่ายไฟแยก

### อุปกรณ์ที่ต้องใช้

1. **ESP32 Development Board**
2. **MAX3232 หรือ SP3232 RS232 Level Converter**
3. **RJ45 Connector และสายไฟ**
4. **Jumper Wires**
5. **Breadboard หรือ PCB** (ตัวเลือก)
6. **Capacitor 100nF** (สำหรับกรอง noise - ตัวเลือก)

### การทดสอบการเชื่อมต่อ

1. ตรวจสอบแรงดันไฟฟ้าที่ RJ45 pins
2. ทดสอบ RS232 converter ด้วย multimeter
3. ตรวจสอบสัญญาณ TTL ที่ ESP32 pins
4. ใช้ Serial Monitor เพื่อดูข้อมูลที่รับได้

### การแก้ไขปัญหา

#### ไม่มีข้อมูล
- ตรวจสอบการเชื่อมต่อสายไฟ
- ตรวจสอบ Baud Rate และ Slave ID
- ตรวจสอบ RS232 converter

#### ข้อมูลผิดพลาด
- ตรวจสอบ TX/RX ว่าสลับกันหรือไม่
- ตรวจสอบ Ground connection
- เพิ่ม capacitor สำหรับกรอง noise
