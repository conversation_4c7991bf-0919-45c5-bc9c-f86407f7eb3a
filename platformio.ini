[env:esp32dev-smart]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
build_src_filter = +<smart_decoder.cpp> -<main.cpp> -<debug_main.cpp> -<hardware_test.cpp> -<protocol_test.cpp> -<data_decoder.cpp>

[env:esp32dev-decoder]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
build_src_filter = +<data_decoder.cpp> -<main.cpp> -<debug_main.cpp> -<hardware_test.cpp> -<protocol_test.cpp> -<smart_decoder.cpp>

[env:esp32dev-protocol]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
build_src_filter = +<protocol_test.cpp> -<main.cpp> -<debug_main.cpp> -<hardware_test.cpp> -<data_decoder.cpp>

[env:esp32dev-hardware]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
build_src_filter = +<hardware_test.cpp> -<main.cpp> -<debug_main.cpp> -<protocol_test.cpp>

[env:esp32dev-debug]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
build_src_filter = +<debug_main.cpp> -<main.cpp> -<hardware_test.cpp>
lib_deps =
    emelianov/modbus-esp8266

[env:esp32dev-main]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
build_src_filter = +<main.cpp> -<debug_main.cpp> -<hardware_test.cpp>
lib_deps =
    emelianov/modbus-esp8266