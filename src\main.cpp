#include <HardwareSerial.h>
#include <ModbusRTU.h>

// Hardware configuration
#define RXD2 16
#define TXD2 17

// Communication settings
#define INVERTER_ID_PRIMARY 5    // Primary slave ID
#define INVERTER_ID_SECONDARY 1  // Secondary slave ID to try
#define BAUD_RATE_PRIMARY 2400   // Primary baud rate
#define BAUD_RATE_SECONDARY 9600 // Secondary baud rate

// Modbus settings
#define MODBUS_TIMEOUT 1000      // Timeout in milliseconds
#define MAX_RETRIES 3            // Maximum retry attempts

// Register addresses (based on SMG-II/PowMr protocol)
// Status registers (read-only)
#define REG_OPERATION_MODE 201       // Operation mode
#define REG_MAINS_VOLTAGE 202        // Mains voltage (0.1V)
#define REG_MAINS_FREQUENCY 203      // Mains frequency (0.01Hz)
#define REG_INVERTER_VOLTAGE 205     // Inverter voltage (0.1V)
#define REG_INVERTER_CURRENT 206     // Inverter current (0.1A)
#define REG_INVERTER_FREQUENCY 207   // Inverter frequency (0.01Hz)
#define REG_INVERTER_POWER 208       // Inverter power (1W)
#define REG_OUTPUT_VOLTAGE 210       // Output voltage (0.1V)
#define REG_OUTPUT_CURRENT 211       // Output current (0.1A)
#define REG_OUTPUT_FREQUENCY 212     // Output frequency (0.01Hz)
#define REG_OUTPUT_POWER 213         // Output power (1W)
#define REG_BATTERY_VOLTAGE 215      // Battery voltage (0.1V)
#define REG_BATTERY_CURRENT 216      // Battery current (0.1A)
#define REG_BATTERY_POWER 217        // Battery power (1W)
#define REG_PV_VOLTAGE 219           // PV voltage (0.1V)
#define REG_PV_CURRENT 220           // PV current (0.1A)
#define REG_PV_POWER 223             // PV power (1W)
#define REG_LOAD_PERCENTAGE 225      // Load percentage (1%)
#define REG_DCDC_TEMPERATURE 226     // DCDC temperature (1°C)
#define REG_INVERTER_TEMPERATURE 227 // Inverter temperature (1°C)
#define REG_BATTERY_SOC 229          // Battery SOC (1%)
#define REG_BATTERY_CURRENT_2 232    // Battery current alternative (0.1A)

// Control registers (read/write)
#define REG_OUTPUT_PRIORITY 301      // Output priority
#define REG_BUZZER_MODE 303          // Buzzer mode
#define REG_OUTPUT_VOLTAGE_SET 320   // Output voltage setting (0.1V)
#define REG_BATTERY_CHARGING_PRIORITY 331 // Battery charging priority
#define REG_MAX_CHARGING_CURRENT 332 // Maximum charging current (0.1A)
#define REG_REMOTE_SWITCH 420        // Remote switch

ModbusRTU mb;

// Global variables
uint8_t currentSlaveId = INVERTER_ID_PRIMARY;
uint32_t currentBaudRate = BAUD_RATE_PRIMARY;
bool connectionEstablished = false;
unsigned long lastSuccessfulRead = 0;

// Function prototypes
bool initializeConnection();
bool readRegisters(uint16_t startAddr, uint16_t numRegs, uint16_t* data);
void readAndDisplayInverterData();
void printConnectionStatus();
float applyScaling(uint16_t value, float scale);
void handleSerialCommands();
bool writeRegister(uint16_t regAddr, uint16_t value);
bool setChargeCurrentLimit(uint16_t current);
bool setOutputSourcePriority(uint16_t priority);
bool setChargerSourcePriority(uint16_t priority);
bool setBuzzerAlarm(uint16_t enable);
void printHelp();
String getOperationModeText(uint16_t mode);

void setup() {
  Serial.begin(115200);
  Serial.println("=== PowMr 6.2kW Inverter Modbus RTU Monitor ===");
  Serial.println("Initializing communication...");

  // Initialize connection with auto-detection
  if (initializeConnection()) {
    Serial.println("Connection established successfully!");
    connectionEstablished = true;
  } else {
    Serial.println("Failed to establish connection. Will retry in loop.");
  }

  delay(2000);
}

bool initializeConnection() {
  // Try different combinations of slave ID and baud rate
  uint8_t slaveIds[] = {INVERTER_ID_PRIMARY, INVERTER_ID_SECONDARY};
  uint32_t baudRates[] = {BAUD_RATE_PRIMARY, BAUD_RATE_SECONDARY};

  for (int i = 0; i < 2; i++) {
    for (int j = 0; j < 2; j++) {
      currentSlaveId = slaveIds[i];
      currentBaudRate = baudRates[j];

      Serial.print("Trying Slave ID: ");
      Serial.print(currentSlaveId);
      Serial.print(", Baud Rate: ");
      Serial.println(currentBaudRate);

      // Initialize serial communication
      Serial2.end();
      delay(100);
      Serial2.begin(currentBaudRate, SERIAL_8N1, RXD2, TXD2);
      mb.begin(&Serial2);
      delay(500);

      // Test connection by reading operation mode register
      uint16_t testData[3];
      if (readRegisters(REG_OPERATION_MODE, 3, testData)) {
        Serial.println("Connection test successful!");
        Serial.print("Operation Mode: ");
        Serial.println(testData[0]);
        return true;
      }

      delay(1000);
    }
  }

  return false;
}

bool readRegisters(uint16_t startAddr, uint16_t numRegs, uint16_t* data) {
  for (int retry = 0; retry < MAX_RETRIES; retry++) {
    bool success = mb.readHreg(currentSlaveId, startAddr, data, numRegs, nullptr);

    if (success) {
      // Validate data (filter out unrealistic values)
      bool dataValid = true;
      for (uint16_t i = 0; i < numRegs; i++) {
        if (data[i] > 65000) { // Filter out invalid high values
          dataValid = false;
          break;
        }
      }

      if (dataValid) {
        lastSuccessfulRead = millis();
        if (!connectionEstablished) {
          connectionEstablished = true;
          Serial.println("Connection re-established!");
        }
        return true;
      }
    }

    if (retry < MAX_RETRIES - 1) {
      delay(100); // Short delay before retry
    }
  }

  // Mark connection as lost if we can't read
  if (connectionEstablished) {
    connectionEstablished = false;
    Serial.print("Connection lost! Failed to read registers ");
    Serial.print(startAddr);
    Serial.print("-");
    Serial.println(startAddr + numRegs - 1);
  }

  return false;
}

void loop() {
  static unsigned long lastReadTime = 0;
  static unsigned long lastStatusTime = 0;
  static bool helpShown = false;

  // Show help message once at startup
  if (!helpShown && connectionEstablished) {
    Serial.println("\nType 'help' for available commands");
    helpShown = true;
  }

  // Handle serial commands
  handleSerialCommands();

  // Check connection status every 30 seconds
  if (millis() - lastStatusTime > 30000) {
    printConnectionStatus();
    lastStatusTime = millis();
  }

  // If no connection, try to re-establish
  if (!connectionEstablished) {
    Serial.println("Attempting to re-establish connection...");
    if (initializeConnection()) {
      Serial.println("Connection re-established!");
      connectionEstablished = true;
    } else {
      delay(5000);
      return;
    }
  }

  // Read data every 5 seconds
  if (millis() - lastReadTime > 5000) {
    Serial.println("\n=== Reading PowMr Inverter Data ===");

    // Read individual registers according to SMG-II protocol
    readAndDisplayInverterData();

    lastReadTime = millis();
  }

  mb.task();
  delay(100);
}

// Helper function to apply scaling factor
float applyScaling(uint16_t value, float scale) {
  return (float)value * scale;
}

// Function to print connection status
void printConnectionStatus() {
  Serial.println("\n=== Connection Status ===");
  Serial.print("Status: ");
  Serial.println(connectionEstablished ? "CONNECTED" : "DISCONNECTED");
  Serial.print("Slave ID: ");
  Serial.println(currentSlaveId);
  Serial.print("Baud Rate: ");
  Serial.println(currentBaudRate);

  if (connectionEstablished) {
    Serial.print("Last successful read: ");
    Serial.print((millis() - lastSuccessfulRead) / 1000);
    Serial.println(" seconds ago");
  }
  Serial.println("========================");
}

// Function to read and display inverter data using SMG-II protocol
void readAndDisplayInverterData() {
  uint16_t data[1];

  // Read Operation Mode
  if (readRegisters(REG_OPERATION_MODE, 1, data)) {
    Serial.print("Operation Mode: ");
    Serial.println(getOperationModeText(data[0]));
  }

  // Read Battery Information
  Serial.println("\nBATTERY:");
  if (readRegisters(REG_BATTERY_VOLTAGE, 1, data)) {
    float voltage = applyScaling(data[0], 0.1);
    Serial.print("  Voltage: ");
    Serial.print(voltage, 1);
    Serial.println(" V");
  }

  if (readRegisters(REG_BATTERY_CURRENT, 1, data)) {
    float current = applyScaling(data[0], 0.1);
    Serial.print("  Current: ");
    Serial.print(current, 1);
    Serial.println(" A");
  }

  if (readRegisters(REG_BATTERY_POWER, 1, data)) {
    Serial.print("  Power: ");
    Serial.print(data[0]);
    Serial.println(" W");
  }

  if (readRegisters(REG_BATTERY_SOC, 1, data)) {
    Serial.print("  State of Charge: ");
    Serial.print(data[0]);
    Serial.println(" %");
  }

  // Read PV Information
  Serial.println("\nSOLAR PV:");
  if (readRegisters(REG_PV_VOLTAGE, 1, data)) {
    float voltage = applyScaling(data[0], 0.1);
    Serial.print("  Voltage: ");
    Serial.print(voltage, 1);
    Serial.println(" V");
  }

  if (readRegisters(REG_PV_CURRENT, 1, data)) {
    float current = applyScaling(data[0], 0.1);
    Serial.print("  Current: ");
    Serial.print(current, 1);
    Serial.println(" A");
  }

  if (readRegisters(REG_PV_POWER, 1, data)) {
    Serial.print("  Power: ");
    Serial.print(data[0]);
    Serial.println(" W");
  }

  // Read Mains/Grid Information
  Serial.println("\nMAINS/GRID:");
  if (readRegisters(REG_MAINS_VOLTAGE, 1, data)) {
    float voltage = applyScaling(data[0], 0.1);
    Serial.print("  Voltage: ");
    Serial.print(voltage, 1);
    Serial.println(" V");
  }

  if (readRegisters(REG_MAINS_FREQUENCY, 1, data)) {
    float frequency = applyScaling(data[0], 0.01);
    Serial.print("  Frequency: ");
    Serial.print(frequency, 2);
    Serial.println(" Hz");
  }

  // Read Output Information
  Serial.println("\nOUTPUT:");
  if (readRegisters(REG_OUTPUT_VOLTAGE, 1, data)) {
    float voltage = applyScaling(data[0], 0.1);
    Serial.print("  Voltage: ");
    Serial.print(voltage, 1);
    Serial.println(" V");
  }

  if (readRegisters(REG_OUTPUT_CURRENT, 1, data)) {
    float current = applyScaling(data[0], 0.1);
    Serial.print("  Current: ");
    Serial.print(current, 1);
    Serial.println(" A");
  }

  if (readRegisters(REG_OUTPUT_POWER, 1, data)) {
    Serial.print("  Power: ");
    Serial.print(data[0]);
    Serial.println(" W");
  }

  if (readRegisters(REG_OUTPUT_FREQUENCY, 1, data)) {
    float frequency = applyScaling(data[0], 0.01);
    Serial.print("  Frequency: ");
    Serial.print(frequency, 2);
    Serial.println(" Hz");
  }

  // Read Load and Temperature Information
  Serial.println("\nSYSTEM STATUS:");
  if (readRegisters(REG_LOAD_PERCENTAGE, 1, data)) {
    Serial.print("  Load: ");
    Serial.print(data[0]);
    Serial.println(" %");
  }

  if (readRegisters(REG_DCDC_TEMPERATURE, 1, data)) {
    Serial.print("  DCDC Temperature: ");
    Serial.print(data[0]);
    Serial.println(" °C");
  }

  if (readRegisters(REG_INVERTER_TEMPERATURE, 1, data)) {
    Serial.print("  Inverter Temperature: ");
    Serial.print(data[0]);
    Serial.println(" °C");
  }

  Serial.println("==========================================");
}

// Function to convert operation mode number to text
String getOperationModeText(uint16_t mode) {
  switch (mode) {
    case 0: return "Power On";
    case 1: return "Standby";
    case 2: return "Mains";
    case 3: return "Off-Grid";
    case 4: return "Bypass";
    case 5: return "Charging";
    case 6: return "Fault";
    default: return "Unknown (" + String(mode) + ")";
  }
}

// Function to write control registers
bool writeRegister(uint16_t regAddr, uint16_t value) {
  for (int retry = 0; retry < MAX_RETRIES; retry++) {
    bool success = mb.writeHreg(currentSlaveId, regAddr, value, nullptr);

    if (success) {
      Serial.print("Successfully wrote value ");
      Serial.print(value);
      Serial.print(" to register ");
      Serial.println(regAddr);
      return true;
    }

    if (retry < MAX_RETRIES - 1) {
      delay(100);
    }
  }

  Serial.print("Failed to write to register ");
  Serial.println(regAddr);
  return false;
}

// Function to handle serial commands for control
void handleSerialCommands() {
  if (Serial.available()) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    command.toLowerCase();

    if (command == "help" || command == "h") {
      printHelp();
    }
    else if (command == "status" || command == "s") {
      printConnectionStatus();
    }
    else if (command.startsWith("set_charge_current ")) {
      int current = command.substring(19).toInt();
      setChargeCurrentLimit(current);
    }
    else if (command.startsWith("set_output_priority ")) {
      int priority = command.substring(20).toInt();
      setOutputSourcePriority(priority);
    }
    else if (command.startsWith("set_charger_priority ")) {
      int priority = command.substring(21).toInt();
      setChargerSourcePriority(priority);
    }
    else if (command == "buzzer_on") {
      setBuzzerAlarm(1);
    }
    else if (command == "buzzer_off") {
      setBuzzerAlarm(0);
    }
    else if (command == "reconnect") {
      connectionEstablished = false;
      Serial.println("Forcing reconnection...");
    }
    else {
      Serial.println("Unknown command. Type 'help' for available commands.");
    }
  }
}

// Control functions based on SMG-II protocol
bool setChargeCurrentLimit(uint16_t current) {
  // Register 332: Maximum charging current (0.1A)
  // Convert from A to 0.1A units
  uint16_t currentValue = current * 10;

  if (current < 1 || current > 120) {
    Serial.println("Invalid charge current. Valid range: 1-120 A");
    return false;
  }

  return writeRegister(REG_MAX_CHARGING_CURRENT, currentValue);
}

bool setOutputSourcePriority(uint16_t priority) {
  // Register 301: Output priority (0-2)
  // 0: Utility-PV-Battery, 1: PV-Utility-Battery, 2: PV-Battery-Utility
  if (priority > 2) {
    Serial.println("Invalid output priority. Valid values:");
    Serial.println("0: Utility-PV-Battery");
    Serial.println("1: PV-Utility-Battery");
    Serial.println("2: PV-Battery-Utility");
    return false;
  }

  return writeRegister(REG_OUTPUT_PRIORITY, priority);
}

bool setChargerSourcePriority(uint16_t priority) {
  // Register 331: Battery charging priority (0-3)
  // 0: Utility priority, 1: PV priority, 2: PV same as Utility, 3: Only PV charging
  if (priority > 3) {
    Serial.println("Invalid charger priority. Valid values:");
    Serial.println("0: Utility priority");
    Serial.println("1: PV priority");
    Serial.println("2: PV same as Utility");
    Serial.println("3: Only PV charging");
    return false;
  }

  return writeRegister(REG_BATTERY_CHARGING_PRIORITY, priority);
}

bool setBuzzerAlarm(uint16_t enable) {
  // Register 303: Buzzer mode (0-3)
  if (enable > 3) {
    Serial.println("Invalid buzzer setting. Valid values:");
    Serial.println("0: Mute in all situations");
    Serial.println("1: Sound when input source changed or warning/fault");
    Serial.println("2: Sound when warning/fault");
    Serial.println("3: Sound when fault occurs");
    return false;
  }

  return writeRegister(REG_BUZZER_MODE, enable);
}

void printHelp() {
  Serial.println("\n=== PowMr Inverter Control Commands (SMG-II Protocol) ===");
  Serial.println("help or h                    - Show this help");
  Serial.println("status or s                  - Show connection status");
  Serial.println("reconnect                    - Force reconnection");
  Serial.println("set_charge_current <value>   - Set max charge current (1-120 A)");
  Serial.println("set_output_priority <value>  - Set output priority:");
  Serial.println("                               0: Utility-PV-Battery");
  Serial.println("                               1: PV-Utility-Battery");
  Serial.println("                               2: PV-Battery-Utility");
  Serial.println("set_charger_priority <value> - Set charger priority:");
  Serial.println("                               0: Utility priority");
  Serial.println("                               1: PV priority");
  Serial.println("                               2: PV same as Utility");
  Serial.println("                               3: Only PV charging");
  Serial.println("buzzer_off                   - Set buzzer to mute");
  Serial.println("buzzer_on                    - Set buzzer to sound on fault");
  Serial.println("========================================================");
}