#include <HardwareSerial.h>
#include <ModbusRTU.h>

// Hardware configuration
#define RXD2 16
#define TXD2 17

// Communication settings
#define INVERTER_ID_PRIMARY 5    // Primary slave ID
#define INVERTER_ID_SECONDARY 1  // Secondary slave ID to try
#define BAUD_RATE_PRIMARY 2400   // Primary baud rate
#define BAUD_RATE_SECONDARY 9600 // Secondary baud rate

// Modbus settings
#define MODBUS_TIMEOUT 1000      // Timeout in milliseconds
#define MAX_RETRIES 3            // Maximum retry attempts

// Register addresses (based on PowMr documentation)
#define REG_STATUS_START 4501    // Main status registers (45 registers)
#define REG_STATUS_COUNT 45
#define REG_EXTENDED_START 4546  // Extended status registers (16 registers)
#define REG_EXTENDED_COUNT 16
#define REG_CONTROL_START 5000   // Control registers

ModbusRTU mb;

// Global variables
uint8_t currentSlaveId = INVERTER_ID_PRIMARY;
uint32_t currentBaudRate = BAUD_RATE_PRIMARY;
bool connectionEstablished = false;
unsigned long lastSuccessfulRead = 0;

// Function prototypes
bool initializeConnection();
bool readRegisters(uint16_t startAddr, uint16_t numRegs, uint16_t* data);
void printInverterData(uint16_t* statusData, uint16_t* extendedData);
void printConnectionStatus();
float applyScaling(uint16_t value, float scale);
void handleSerialCommands();
bool writeRegister(uint16_t regAddr, uint16_t value);
bool setChargeCurrentLimit(uint16_t current);
bool setOutputSourcePriority(uint16_t priority);
bool setChargerSourcePriority(uint16_t priority);
bool setBuzzerAlarm(uint16_t enable);
void printHelp();

void setup() {
  Serial.begin(115200);
  Serial.println("=== PowMr 6.2kW Inverter Modbus RTU Monitor ===");
  Serial.println("Initializing communication...");

  // Initialize connection with auto-detection
  if (initializeConnection()) {
    Serial.println("Connection established successfully!");
    connectionEstablished = true;
  } else {
    Serial.println("Failed to establish connection. Will retry in loop.");
  }

  delay(2000);
}

bool initializeConnection() {
  // Try different combinations of slave ID and baud rate
  uint8_t slaveIds[] = {INVERTER_ID_PRIMARY, INVERTER_ID_SECONDARY};
  uint32_t baudRates[] = {BAUD_RATE_PRIMARY, BAUD_RATE_SECONDARY};

  for (int i = 0; i < 2; i++) {
    for (int j = 0; j < 2; j++) {
      currentSlaveId = slaveIds[i];
      currentBaudRate = baudRates[j];

      Serial.print("Trying Slave ID: ");
      Serial.print(currentSlaveId);
      Serial.print(", Baud Rate: ");
      Serial.println(currentBaudRate);

      // Initialize serial communication
      Serial2.end();
      delay(100);
      Serial2.begin(currentBaudRate, SERIAL_8N1, RXD2, TXD2);
      mb.begin(&Serial2);
      delay(500);

      // Test connection by reading a small number of registers
      uint16_t testData[5];
      if (readRegisters(REG_STATUS_START, 5, testData)) {
        Serial.println("Connection test successful!");
        return true;
      }

      delay(1000);
    }
  }

  return false;
}

bool readRegisters(uint16_t startAddr, uint16_t numRegs, uint16_t* data) {
  for (int retry = 0; retry < MAX_RETRIES; retry++) {
    bool success = mb.readHreg(currentSlaveId, startAddr, data, numRegs, nullptr);

    if (success) {
      // Validate data (filter out unrealistic values)
      bool dataValid = true;
      for (uint16_t i = 0; i < numRegs; i++) {
        if (data[i] > 65000) { // Filter out invalid high values
          dataValid = false;
          break;
        }
      }

      if (dataValid) {
        lastSuccessfulRead = millis();
        if (!connectionEstablished) {
          connectionEstablished = true;
          Serial.println("Connection re-established!");
        }
        return true;
      }
    }

    if (retry < MAX_RETRIES - 1) {
      delay(100); // Short delay before retry
    }
  }

  // Mark connection as lost if we can't read
  if (connectionEstablished) {
    connectionEstablished = false;
    Serial.print("Connection lost! Failed to read registers ");
    Serial.print(startAddr);
    Serial.print("-");
    Serial.println(startAddr + numRegs - 1);
  }

  return false;
}

void loop() {
  static unsigned long lastReadTime = 0;
  static unsigned long lastStatusTime = 0;
  static bool helpShown = false;

  // Show help message once at startup
  if (!helpShown && connectionEstablished) {
    Serial.println("\nType 'help' for available commands");
    helpShown = true;
  }

  // Handle serial commands
  handleSerialCommands();

  // Check connection status every 30 seconds
  if (millis() - lastStatusTime > 30000) {
    printConnectionStatus();
    lastStatusTime = millis();
  }

  // If no connection, try to re-establish
  if (!connectionEstablished) {
    Serial.println("Attempting to re-establish connection...");
    if (initializeConnection()) {
      Serial.println("Connection re-established!");
      connectionEstablished = true;
    } else {
      delay(5000);
      return;
    }
  }

  // Read data every 5 seconds
  if (millis() - lastReadTime > 5000) {
    uint16_t statusData[REG_STATUS_COUNT];
    uint16_t extendedData[REG_EXTENDED_COUNT];

    Serial.println("\n=== Reading PowMr Inverter Data ===");

    bool statusSuccess = readRegisters(REG_STATUS_START, REG_STATUS_COUNT, statusData);
    bool extendedSuccess = readRegisters(REG_EXTENDED_START, REG_EXTENDED_COUNT, extendedData);

    if (statusSuccess || extendedSuccess) {
      printInverterData(statusSuccess ? statusData : nullptr,
                       extendedSuccess ? extendedData : nullptr);
    }

    lastReadTime = millis();
  }

  mb.task();
  delay(100);
}

// Helper function to apply scaling factor
float applyScaling(uint16_t value, float scale) {
  return (float)value * scale;
}

// Function to print connection status
void printConnectionStatus() {
  Serial.println("\n=== Connection Status ===");
  Serial.print("Status: ");
  Serial.println(connectionEstablished ? "CONNECTED" : "DISCONNECTED");
  Serial.print("Slave ID: ");
  Serial.println(currentSlaveId);
  Serial.print("Baud Rate: ");
  Serial.println(currentBaudRate);

  if (connectionEstablished) {
    Serial.print("Last successful read: ");
    Serial.print((millis() - lastSuccessfulRead) / 1000);
    Serial.println(" seconds ago");
  }
  Serial.println("========================");
}

// Function to print inverter data in organized format
void printInverterData(uint16_t* statusData, uint16_t* extendedData) {
  if (statusData) {
    Serial.println("\n--- Main Status Data (Registers 4501-4545) ---");

    // Battery Information
    Serial.println("BATTERY:");
    Serial.print("  Voltage: ");
    Serial.print(applyScaling(statusData[0], 0.01), 2);
    Serial.println(" V");

    Serial.print("  Current: ");
    Serial.print(applyScaling(statusData[1], 0.01), 2);
    Serial.println(" A");

    Serial.print("  Capacity: ");
    Serial.print(statusData[2]);
    Serial.println(" %");

    // PV Information
    Serial.println("SOLAR PV:");
    Serial.print("  Voltage: ");
    Serial.print(applyScaling(statusData[8], 0.1), 1);
    Serial.println(" V");

    Serial.print("  Current: ");
    Serial.print(applyScaling(statusData[9], 0.01), 2);
    Serial.println(" A");

    Serial.print("  Power: ");
    Serial.print(statusData[10]);
    Serial.println(" W");

    // Grid Information
    Serial.println("GRID:");
    Serial.print("  Voltage: ");
    Serial.print(applyScaling(statusData[6], 0.1), 1);
    Serial.println(" V");

    Serial.print("  Frequency: ");
    Serial.print(applyScaling(statusData[7], 0.01), 2);
    Serial.println(" Hz");

    // Inverter Output
    Serial.println("INVERTER OUTPUT:");
    Serial.print("  Voltage: ");
    Serial.print(applyScaling(statusData[3], 0.1), 1);
    Serial.println(" V");

    Serial.print("  Current: ");
    Serial.print(applyScaling(statusData[4], 0.01), 2);
    Serial.println(" A");

    Serial.print("  Power: ");
    Serial.print(statusData[5]);
    Serial.println(" W");

    // Additional status information
    if (REG_STATUS_COUNT > 15) {
      Serial.println("ADDITIONAL STATUS:");
      Serial.print("  Temperature: ");
      Serial.print(applyScaling(statusData[15], 0.1), 1);
      Serial.println(" °C");

      Serial.print("  Status Code: ");
      Serial.println(statusData[11]);

      Serial.print("  Fault Code: ");
      Serial.println(statusData[12]);
    }
  }

  if (extendedData) {
    Serial.println("\n--- Extended Data (Registers 4546-4561) ---");

    for (int i = 0; i < REG_EXTENDED_COUNT; i++) {
      Serial.print("  Reg ");
      Serial.print(REG_EXTENDED_START + i);
      Serial.print(": ");
      Serial.println(extendedData[i]);
    }
  }

  Serial.println("==========================================");
}

// Function to write control registers
bool writeRegister(uint16_t regAddr, uint16_t value) {
  for (int retry = 0; retry < MAX_RETRIES; retry++) {
    bool success = mb.writeHreg(currentSlaveId, regAddr, value, nullptr);

    if (success) {
      Serial.print("Successfully wrote value ");
      Serial.print(value);
      Serial.print(" to register ");
      Serial.println(regAddr);
      return true;
    }

    if (retry < MAX_RETRIES - 1) {
      delay(100);
    }
  }

  Serial.print("Failed to write to register ");
  Serial.println(regAddr);
  return false;
}

// Function to handle serial commands for control
void handleSerialCommands() {
  if (Serial.available()) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    command.toLowerCase();

    if (command == "help" || command == "h") {
      printHelp();
    }
    else if (command == "status" || command == "s") {
      printConnectionStatus();
    }
    else if (command.startsWith("set_charge_current ")) {
      int current = command.substring(19).toInt();
      setChargeCurrentLimit(current);
    }
    else if (command.startsWith("set_output_priority ")) {
      int priority = command.substring(20).toInt();
      setOutputSourcePriority(priority);
    }
    else if (command.startsWith("set_charger_priority ")) {
      int priority = command.substring(21).toInt();
      setChargerSourcePriority(priority);
    }
    else if (command == "buzzer_on") {
      setBuzzerAlarm(1);
    }
    else if (command == "buzzer_off") {
      setBuzzerAlarm(0);
    }
    else if (command == "reconnect") {
      connectionEstablished = false;
      Serial.println("Forcing reconnection...");
    }
    else {
      Serial.println("Unknown command. Type 'help' for available commands.");
    }
  }
}

// Control functions based on PowMr documentation
bool setChargeCurrentLimit(uint16_t current) {
  // Register 5024: Utility charge current (2, 10, 20, 30, 40, 50, 60)
  uint16_t validCurrents[] = {2, 10, 20, 30, 40, 50, 60};
  bool isValid = false;

  for (int i = 0; i < 7; i++) {
    if (current == validCurrents[i]) {
      isValid = true;
      break;
    }
  }

  if (!isValid) {
    Serial.println("Invalid charge current. Valid values: 2, 10, 20, 30, 40, 50, 60");
    return false;
  }

  return writeRegister(5024, current);
}

bool setOutputSourcePriority(uint16_t priority) {
  // Register 5018: Output source priority (0-2)
  // 0: Solar first, 1: Mains first, 2: Battery first
  if (priority > 2) {
    Serial.println("Invalid output priority. Valid values: 0 (Solar first), 1 (Mains first), 2 (Battery first)");
    return false;
  }

  return writeRegister(5018, priority);
}

bool setChargerSourcePriority(uint16_t priority) {
  // Register 5017: Charger source priority (0-3)
  // 0: Solar first, 1: Mains first, 2: Solar + Mains, 3: Solar only
  if (priority > 3) {
    Serial.println("Invalid charger priority. Valid values: 0-3");
    return false;
  }

  return writeRegister(5017, priority);
}

bool setBuzzerAlarm(uint16_t enable) {
  // Register 5002: Buzzer alarm (0-1)
  if (enable > 1) {
    Serial.println("Invalid buzzer setting. Valid values: 0 (Off), 1 (On)");
    return false;
  }

  return writeRegister(5002, enable);
}

void printHelp() {
  Serial.println("\n=== PowMr Inverter Control Commands ===");
  Serial.println("help or h                    - Show this help");
  Serial.println("status or s                  - Show connection status");
  Serial.println("reconnect                    - Force reconnection");
  Serial.println("set_charge_current <value>   - Set charge current (2,10,20,30,40,50,60)");
  Serial.println("set_output_priority <value>  - Set output priority (0=Solar, 1=Mains, 2=Battery)");
  Serial.println("set_charger_priority <value> - Set charger priority (0=Solar, 1=Mains, 2=Both, 3=Solar only)");
  Serial.println("buzzer_on                    - Enable buzzer alarm");
  Serial.println("buzzer_off                   - Disable buzzer alarm");
  Serial.println("=======================================");
}