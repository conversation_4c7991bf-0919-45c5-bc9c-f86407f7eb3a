#include <HardwareSerial.h>
#include <ModbusRTU.h>

#define RXD2 16
#define TXD2 17
#define INVERTER_ID 5 // อาจลอง 1 หาก 5 ไม่ทำงาน
#define BAUD_RATE 2400 // อาจลอง 9600 หาก 2400 ไม่ทำงาน

ModbusRTU mb;

void setup() {
  Serial.begin(115200);
  Serial2.begin(BAUD_RATE, SERIAL_8N1, RXD2, TXD2);
  mb.begin(&Serial2);
  Serial.println("Starting PowMr RS232 communication...");
  delay(1000);
}

bool readRegisters(uint16_t startAddr, uint16_t numRegs, uint16_t* data) {
  bool success = mb.readHreg(INVERTER_ID, startAddr, data, numRegs, nullptr);
  if (success) {
    for (uint16_t i = 0; i < numRegs; i++) {
      if (data[i] > 60000) { // กรองค่าที่สูงเกินจริง
        Serial.print("Invalid data at register ");
        Serial.println(startAddr + i);
        return false;
      }
    }
    return true;
  } else {
    Serial.print("Failed to read address ");
    Serial.print(startAddr);
    Serial.println(": No response or invalid data");
    return false;
  }
}

void loop() {
  uint16_t data[45];
  
  // อ่านจาก register 0x3000 (ตาม ESPHome)
  if (readRegisters(0x3000, 20, data)) {
    Serial.println("Data from address 0x3000:");
    Serial.print("Battery Voltage (V): "); Serial.println(data[0] * 0.01); // Scaling 0.01
    Serial.print("Battery Current (A): "); Serial.println(data[1] * 0.01);
    Serial.print("Battery Capacity (%): "); Serial.println(data[2]);
    Serial.print("Inverter Output Voltage (V): "); Serial.println(data[3] * 0.1);
    Serial.print("Inverter Output Current (A): "); Serial.println(data[4] * 0.01);
    Serial.print("Inverter Output Power (W): "); Serial.println(data[5]);
    Serial.print("Grid Voltage (V): "); Serial.println(data[6] * 0.1);
    Serial.print("Grid Frequency (Hz): "); Serial.println(data[7] * 0.01);
    Serial.print("PV Voltage (V): "); Serial.println(data[8] * 0.1);
    Serial.print("PV Current (A): "); Serial.println(data[9] * 0.01);
    Serial.print("PV Power (W): "); Serial.println(data[10]);
  }

  // อ่านจาก register 0x3100
  if (readRegisters(0x3100, 10, data)) {
    Serial.println("Data from address 0x3100:");
    Serial.print("Inverter Status: "); Serial.println(data[0]);
    Serial.print("Battery Charge Status: "); Serial.println(data[1]);
    Serial.print("Temperature (°C): "); Serial.println(data[2] * 0.1);
  }

  mb.task();
  delay(5000);
}