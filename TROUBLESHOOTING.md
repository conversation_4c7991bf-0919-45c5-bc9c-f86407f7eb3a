# การแก้ไขปัญหาการเชื่อมต่อ PowMr Inverter

## ปัญหาที่พบ: ไม่ได้รับข้อมูลจาก Inverter

จากข้อมูลที่ให้มา:
- แบตเตอรี่: 53.8V
- Input: 28V

แสดงว่า Inverter ทำงานอยู่ แต่การสื่อสารผ่าน Modbus มีปัญหา

## ขั้นตอนการแก้ไขปัญหา

### 1. ตรวจสอบการเชื่อมต่อฮาร์ดแวร์

#### การเชื่อมต่อ RJ45
```
PowMr RJ45 Pin → RS232 Converter → ESP32
Pin 1 (TX)     → RX              → GPIO16 (RX2)
Pin 2 (RX)     → TX              → GPIO17 (TX2)
Pin 4 (+12V)   → VCC             → 3.3V
Pin 8 (GND)    → GND             → GND
```

#### ตรวจสอบด้วย Multimeter
1. **ตรวจสอบแรงดันไฟ**: Pin 4 ควรมี +12V, Pin 8 เป็น GND
2. **ตรวจสอบสัญญาณ RS232**: Pin 1, 2 ควรมีสัญญาณ ±12V
3. **ตรวจสอบสัญญาณ TTL**: ที่ ESP32 ควรมีสัญญาณ 0-3.3V

### 2. ใช้ Debug Tool

#### รัน Debug Version
```bash
# Copy debug configuration
cp platformio_debug.ini platformio.ini

# Build and upload debug version
pio run -e esp32dev-debug -t upload

# Monitor output
pio device monitor
```

#### ผลลัพธ์ที่คาดหวัง
Debug tool จะทดสอบ:
- Slave IDs: 1, 5, 10, 247
- Baud Rates: 2400, 9600, 4800, 19200
- Register addresses: 201, 202, 215, 219, 4501, 4502

หาค่าที่ตรงกับ:
- Battery voltage ~53.8V
- Input voltage ~28V

### 3. การตีความผลลัพธ์ Debug

#### ถ้าพบข้อมูล
```
*** POTENTIAL BATTERY VOLTAGE FOUND! *** Reg 215: 538 (53.8V with 0.1 scale)
```

แสดงว่า:
- Register 215 คือ Battery Voltage
- ใช้ scaling factor 0.1
- Slave ID และ Baud Rate ที่ใช้คือค่าที่ถูกต้อง

#### ถ้าไม่พบข้อมูล
ลองขั้นตอนต่อไป

### 4. ตรวจสอบ Register Addresses อื่นๆ

#### PowMr อาจใช้ register addresses ต่างจากมาตรฐาน
```cpp
// ลองเพิ่ม register addresses เหล่านี้ใน debug tool
uint16_t additionalRegisters[] = {
    0x3000, 0x3001, 0x3002,  // ESPHome style
    4501, 4502, 4503,        // Original PowMr
    12289, 12290, 12291      // Alternative addressing
};
```

### 5. ปรับแต่ง Communication Parameters

#### ลอง Timeout และ Delay ต่างๆ
```cpp
// เพิ่มใน debug code
mb.setTimeout(2000);  // เพิ่ม timeout
delay(1000);          // เพิ่ม delay ระหว่างคำสั่ง
```

#### ลอง Parity และ Stop Bits ต่างๆ
```cpp
// ลองการตั้งค่าต่างๆ
Serial2.begin(2400, SERIAL_8N1, RXD2, TXD2);  // No parity, 1 stop bit
Serial2.begin(2400, SERIAL_8N2, RXD2, TXD2);  // No parity, 2 stop bits
Serial2.begin(2400, SERIAL_8E1, RXD2, TXD2);  // Even parity, 1 stop bit
```

### 6. ตรวจสอบ RF Interference

#### เพิ่ม Capacitor กรอง Noise
- เชื่อม capacitor 100nF ระหว่าง RX pin (GPIO16) กับ GND
- ใช้สาย shielded cable
- ลดระยะห่างระหว่าง ESP32 และ Inverter

### 7. ทดสอบด้วย USB-to-RS232 Adapter

#### ใช้ Computer ทดสอบ
1. เชื่อม USB-to-RS232 adapter กับ Inverter
2. ใช้โปรแกรม Modbus Master (เช่น QModMaster)
3. ทดสอบการอ่านข้อมูลด้วยพารามิเตอร์ต่างๆ

### 8. ตรวจสอบ Inverter Settings

#### เข้าเมนู Inverter
1. กดปุ่มบน Inverter เพื่อเข้าเมนู
2. หา Communication Settings
3. ตรวจสอบ:
   - Modbus Address (Slave ID)
   - Baud Rate
   - Communication Protocol (ต้องเป็น Modbus RTU)

### 9. ลองใช้ ESPHome Configuration

#### ถ้าเป็น PowMr รุ่นที่รองรับ ESPHome
```yaml
# ลองใช้ configuration จาก ESPHome
modbus:
  uart_id: uart_bus
  
sensor:
  - platform: modbus_controller
    modbus_controller_id: powmr
    name: "Battery Voltage"
    address: 0x3000
    register_type: holding
    value_type: U_WORD
    accuracy_decimals: 1
    filters:
      - multiply: 0.01
```

### 10. ขั้นตอนสุดท้าย

#### ถ้าทุกอย่างไม่ได้ผล
1. **ตรวจสอบ Inverter Model**: ให้แน่ใจว่าเป็นรุ่นที่รองรับ Modbus
2. **ตรวจสอบ Firmware Version**: อาจต้องอัพเดท firmware
3. **ติดต่อ PowMr Support**: ขอ communication protocol document
4. **ใช้ Logic Analyzer**: วิเคราะห์สัญญาณ RS232 ระดับต่ำ

## ตัวอย่างผลลัพธ์ที่ถูกต้อง

```
Testing Baud Rate: 2400, Slave ID: 5
  Testing register 215: SUCCESS - Values: 538, 152, 85, 230, 502
    *** POTENTIAL BATTERY VOLTAGE FOUND! *** Reg 215: 538 (53.8V with 0.1 scale)
  Testing register 219: SUCCESS - Values: 280, 85, 554, 0, 0
    *** POTENTIAL INPUT VOLTAGE FOUND! *** Reg 219: 280 (28.0V with 0.1 scale)
```

## การอัพเดทโค้ดหลัก

เมื่อพบพารามิเตอร์ที่ถูกต้องแล้ว ให้อัพเดทในไฟล์ `src/main.cpp`:

```cpp
// อัพเดทค่าเหล่านี้ตามผลลัพธ์ debug
#define INVERTER_ID_PRIMARY 5        // Slave ID ที่ใช้ได้
#define BAUD_RATE_PRIMARY 2400       // Baud rate ที่ใช้ได้
#define REG_BATTERY_VOLTAGE 215      // Register ที่พบแบตเตอรี่
#define REG_PV_VOLTAGE 219           // Register ที่พบ PV voltage
```

## หมายเหตุ

- การ debug อาจใช้เวลา 10-15 นาที
- บันทึกผลลัพธ์ทั้งหมดเพื่อวิเคราะห์
- ถ้าพบข้อมูลบางส่วน แสดงว่าการเชื่อมต่อใช้ได้ แต่ register mapping อาจต่าง
