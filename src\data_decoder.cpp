/*
 * PowMr Data Decoder
 * 
 * Based on the test results, we found that the inverter sends continuous data
 * in a pattern of 0x00 0xFF. This decoder will try to extract meaningful
 * voltage and current data from this stream.
 */

#include <HardwareSerial.h>

#define RXD2 16
#define TXD2 17

// Function prototypes
void runDataDecoder();
void analyzeDataStream();
void extractVoltageData(uint8_t* buffer, int length);
void testModbusCommands();

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=== PowMr Data Decoder ===");
  Serial.println("Attempting to decode real inverter data...");
  Serial.println("Expected: Battery ~53.8V, Input ~28V\n");
  
  runDataDecoder();
}

void loop() {
  // Decoder runs once in setup
  delay(10000);
}

void runDataDecoder() {
  // Initialize with the working baud rate
  Serial2.begin(4800, SERIAL_8N1, RXD2, TXD2);
  delay(1000);
  
  Serial.println("1. Analyzing continuous data stream...");
  analyzeDataStream();
  
  Serial.println("\n2. Testing specific Modbus commands...");
  testModbusCommands();
  
  Serial.println("\n=== Decoder Complete ===");
}

void analyzeDataStream() {
  uint8_t buffer[512];
  int bufferIndex = 0;
  unsigned long startTime = millis();
  
  Serial.println("   Collecting 30 seconds of data for pattern analysis...");
  
  while (millis() - startTime < 30000) {
    if (Serial2.available()) {
      uint8_t data = Serial2.read();
      buffer[bufferIndex] = data;
      bufferIndex++;
      
      // Analyze when buffer is full
      if (bufferIndex >= 512) {
        extractVoltageData(buffer, bufferIndex);
        bufferIndex = 0;
      }
    }
    delay(1);
  }
  
  // Analyze remaining data
  if (bufferIndex > 0) {
    extractVoltageData(buffer, bufferIndex);
  }
}

void extractVoltageData(uint8_t* buffer, int length) {
  Serial.print("   Analyzing ");
  Serial.print(length);
  Serial.println(" bytes...");
  
  // Method 1: Look for 16-bit values that match expected voltages
  for (int i = 0; i < length - 1; i++) {
    uint16_t value16_be = (buffer[i] << 8) | buffer[i + 1];  // Big endian
    uint16_t value16_le = (buffer[i + 1] << 8) | buffer[i];  // Little endian
    
    // Test different scaling factors
    float voltages_be[4];
    voltages_be[0] = value16_be * 0.1f;
    voltages_be[1] = value16_be * 0.01f;
    voltages_be[2] = value16_be / 10.0f;
    voltages_be[3] = value16_be / 100.0f;

    float voltages_le[4];
    voltages_le[0] = value16_le * 0.1f;
    voltages_le[1] = value16_le * 0.01f;
    voltages_le[2] = value16_le / 10.0f;
    voltages_le[3] = value16_le / 100.0f;
    
    // Check for battery voltage (53.8V ±3V)
    for (int j = 0; j < 4; j++) {
      if (voltages_be[j] >= 50.8 && voltages_be[j] <= 56.8) {
        Serial.print("   *** BATTERY VOLTAGE FOUND! *** Position ");
        Serial.print(i);
        Serial.print(": 0x");
        Serial.print(value16_be, HEX);
        Serial.print(" = ");
        Serial.print(voltages_be[j], 2);
        Serial.print("V (BE, scale ");
        Serial.print(j == 0 ? "0.1" : j == 1 ? "0.01" : j == 2 ? "/10" : "/100");
        Serial.println(")");
      }
      
      if (voltages_le[j] >= 50.8 && voltages_le[j] <= 56.8) {
        Serial.print("   *** BATTERY VOLTAGE FOUND! *** Position ");
        Serial.print(i);
        Serial.print(": 0x");
        Serial.print(value16_le, HEX);
        Serial.print(" = ");
        Serial.print(voltages_le[j], 2);
        Serial.print("V (LE, scale ");
        Serial.print(j == 0 ? "0.1" : j == 1 ? "0.01" : j == 2 ? "/10" : "/100");
        Serial.println(")");
      }
    }
    
    // Check for input voltage (28V ±7V)
    for (int j = 0; j < 4; j++) {
      if (voltages_be[j] >= 21 && voltages_be[j] <= 35) {
        Serial.print("   *** INPUT VOLTAGE FOUND! *** Position ");
        Serial.print(i);
        Serial.print(": 0x");
        Serial.print(value16_be, HEX);
        Serial.print(" = ");
        Serial.print(voltages_be[j], 2);
        Serial.print("V (BE, scale ");
        Serial.print(j == 0 ? "0.1" : j == 1 ? "0.01" : j == 2 ? "/10" : "/100");
        Serial.println(")");
      }
      
      if (voltages_le[j] >= 21 && voltages_le[j] <= 35) {
        Serial.print("   *** INPUT VOLTAGE FOUND! *** Position ");
        Serial.print(i);
        Serial.print(": 0x");
        Serial.print(value16_le, HEX);
        Serial.print(" = ");
        Serial.print(voltages_le[j], 2);
        Serial.print("V (LE, scale ");
        Serial.print(j == 0 ? "0.1" : j == 1 ? "0.01" : j == 2 ? "/10" : "/100");
        Serial.println(")");
      }
    }
  }
  
  // Method 2: Look for specific byte patterns
  // Pattern for 53.8V with 0.1 scale = 538 = 0x021A
  // Pattern for 28V with 0.1 scale = 280 = 0x0118
  
  for (int i = 0; i < length - 1; i++) {
    if ((buffer[i] == 0x02 && buffer[i + 1] == 0x1A) ||
        (buffer[i] == 0x1A && buffer[i + 1] == 0x02)) {
      Serial.print("   *** EXACT BATTERY PATTERN! *** Position ");
      Serial.print(i);
      Serial.println(": 0x021A (53.8V)");
    }
    
    if ((buffer[i] == 0x01 && buffer[i + 1] == 0x18) ||
        (buffer[i] == 0x18 && buffer[i + 1] == 0x01)) {
      Serial.print("   *** EXACT INPUT PATTERN! *** Position ");
      Serial.print(i);
      Serial.println(": 0x0118 (28V)");
    }
  }
  
  // Method 3: Look for repeating patterns that might be data frames
  Serial.print("   Data pattern: ");
  int maxShow = (length < 32) ? length : 32;
  for (int i = 0; i < maxShow; i++) {
    Serial.print("0x");
    if (buffer[i] < 0x10) Serial.print("0");
    Serial.print(buffer[i], HEX);
    Serial.print(" ");
  }
  Serial.println("...");
}

void testModbusCommands() {
  // Clear buffer
  while (Serial2.available()) Serial2.read();
  
  // Test successful Modbus command (Function 04, Read Input Registers)
  Serial.println("   Testing Function 04 (Read Input Registers)...");
  
  // Try different register addresses
  uint16_t testAddresses[] = {0x0000, 0x0001, 0x0002, 0x0010, 0x0020, 0x0100, 0x0200};
  
  for (int addr = 0; addr < 7; addr++) {
    uint16_t regAddr = testAddresses[addr];
    
    // Build Modbus frame: [Slave][Func][Addr H][Addr L][Qty H][Qty L][CRC L][CRC H]
    uint8_t frame[8];
    frame[0] = 0x01;  // Slave ID
    frame[1] = 0x04;  // Function 04
    frame[2] = (regAddr >> 8) & 0xFF;  // Address high
    frame[3] = regAddr & 0xFF;         // Address low
    frame[4] = 0x00;  // Quantity high
    frame[5] = 0x02;  // Quantity low (read 2 registers)
    
    // Simple CRC calculation (not accurate, but for testing)
    uint16_t crc = 0xFFFF;
    for (int i = 0; i < 6; i++) {
      crc ^= frame[i];
      for (int j = 0; j < 8; j++) {
        if (crc & 0x0001) {
          crc = (crc >> 1) ^ 0xA001;
        } else {
          crc = crc >> 1;
        }
      }
    }
    frame[6] = crc & 0xFF;
    frame[7] = (crc >> 8) & 0xFF;
    
    Serial.print("   Testing register 0x");
    Serial.print(regAddr, HEX);
    Serial.print(": ");
    
    // Clear buffer
    while (Serial2.available()) Serial2.read();
    
    // Send frame
    for (int i = 0; i < 8; i++) {
      Serial2.write(frame[i]);
    }
    
    delay(500);
    
    // Read response
    if (Serial2.available()) {
      Serial.print("Response: ");
      uint8_t response[20];
      int respLen = 0;
      
      while (Serial2.available() && respLen < 20) {
        response[respLen] = Serial2.read();
        Serial.print("0x");
        if (response[respLen] < 0x10) Serial.print("0");
        Serial.print(response[respLen], HEX);
        Serial.print(" ");
        respLen++;
      }
      Serial.println();
      
      // Analyze response
      if (respLen >= 5 && response[0] == 0x01 && response[1] == 0x04) {
        uint8_t byteCount = response[2];
        Serial.print("      Valid Modbus response! Data bytes: ");
        Serial.println(byteCount);
        
        if (byteCount >= 4) {
          uint16_t reg1 = (response[3] << 8) | response[4];
          uint16_t reg2 = (response[5] << 8) | response[6];
          
          Serial.print("      Register 1: ");
          Serial.print(reg1);
          Serial.print(" (");
          Serial.print(reg1 * 0.1, 1);
          Serial.print("V, ");
          Serial.print(reg1 * 0.01, 2);
          Serial.println("V)");
          
          Serial.print("      Register 2: ");
          Serial.print(reg2);
          Serial.print(" (");
          Serial.print(reg2 * 0.1, 1);
          Serial.print("V, ");
          Serial.print(reg2 * 0.01, 2);
          Serial.println("V)");
          
          // Check if these match our expected values
          float volt1_1 = reg1 * 0.1;
          float volt1_2 = reg1 * 0.01;
          float volt2_1 = reg2 * 0.1;
          float volt2_2 = reg2 * 0.01;
          
          if ((volt1_1 >= 50.8 && volt1_1 <= 56.8) || (volt1_2 >= 50.8 && volt1_2 <= 56.8)) {
            Serial.println("      *** POTENTIAL BATTERY VOLTAGE IN REG 1! ***");
          }
          if ((volt2_1 >= 50.8 && volt2_1 <= 56.8) || (volt2_2 >= 50.8 && volt2_2 <= 56.8)) {
            Serial.println("      *** POTENTIAL BATTERY VOLTAGE IN REG 2! ***");
          }
          if ((volt1_1 >= 21 && volt1_1 <= 35) || (volt1_2 >= 21 && volt1_2 <= 35)) {
            Serial.println("      *** POTENTIAL INPUT VOLTAGE IN REG 1! ***");
          }
          if ((volt2_1 >= 21 && volt2_1 <= 35) || (volt2_2 >= 21 && volt2_2 <= 35)) {
            Serial.println("      *** POTENTIAL INPUT VOLTAGE IN REG 2! ***");
          }
        }
      }
    } else {
      Serial.println("No response");
    }
    
    delay(1000);
  }
}
