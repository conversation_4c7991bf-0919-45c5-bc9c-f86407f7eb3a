/*
 * Hardware Connection Test
 * 
 * This test helps verify the physical connection to the PowMr inverter
 * without using Modbus protocol.
 */

#include <HardwareSerial.h>

#define RXD2 16
#define TXD2 17

// Function prototype
void runHardwareTests();

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=== PowMr Hardware Connection Test ===");
  Serial.println("This test will help verify the physical connection");
  Serial.println("to your PowMr inverter.\n");
  
  runHardwareTests();
}

void loop() {
  // Test runs once in setup
  delay(10000);
}

void runHardwareTests() {
  Serial.println("1. Testing Serial Port Initialization...");
  
  // Test different baud rates
  uint32_t baudRates[] = {2400, 9600, 4800, 19200};
  
  for (int i = 0; i < 4; i++) {
    uint32_t baud = baudRates[i];
    
    Serial.print("   Testing baud rate: ");
    Serial.println(baud);
    
    Serial2.begin(baud, SERIAL_8N1, RXD2, TXD2);
    delay(500);
    
    // Send a simple test byte
    Serial2.write(0x55); // Test pattern
    delay(100);
    
    // Check if anything comes back
    if (Serial2.available()) {
      Serial.print("   -> Received data: ");
      while (Serial2.available()) {
        uint8_t data = Serial2.read();
        Serial.print("0x");
        Serial.print(data, HEX);
        Serial.print(" ");
      }
      Serial.println();
    } else {
      Serial.println("   -> No response");
    }
    
    Serial2.end();
    delay(500);
  }
  
  Serial.println("\n2. Testing Raw Modbus Frame...");
  
  // Initialize with most common settings
  Serial2.begin(2400, SERIAL_8N1, RXD2, TXD2);
  delay(1000);
  
  // Send a raw Modbus RTU frame to read register 201
  // Frame: [Slave ID][Function][Start Addr H][Start Addr L][Qty H][Qty L][CRC L][CRC H]
  uint8_t modbusFrame[] = {0x05, 0x03, 0x00, 0xC9, 0x00, 0x01, 0x54, 0x0A};
  
  Serial.println("   Sending Modbus frame to read register 201 (Slave ID 5):");
  Serial.print("   Frame: ");
  for (int i = 0; i < 8; i++) {
    Serial.print("0x");
    Serial.print(modbusFrame[i], HEX);
    Serial.print(" ");
  }
  Serial.println();
  
  // Clear any existing data
  while (Serial2.available()) {
    Serial2.read();
  }
  
  // Send the frame
  for (int i = 0; i < 8; i++) {
    Serial2.write(modbusFrame[i]);
  }
  
  // Wait for response
  delay(1000);
  
  if (Serial2.available()) {
    Serial.print("   -> Response received: ");
    while (Serial2.available()) {
      uint8_t data = Serial2.read();
      Serial.print("0x");
      Serial.print(data, HEX);
      Serial.print(" ");
    }
    Serial.println();
  } else {
    Serial.println("   -> No response to Modbus frame");
  }
  
  Serial.println("\n3. Testing Different Slave IDs...");
  
  uint8_t slaveIds[] = {1, 5, 10, 247};
  
  for (int s = 0; s < 4; s++) {
    uint8_t slaveId = slaveIds[s];
    
    Serial.print("   Testing Slave ID: ");
    Serial.println(slaveId);
    
    // Create Modbus frame for this slave ID
    uint8_t frame[] = {slaveId, 0x03, 0x00, 0xC9, 0x00, 0x01, 0x00, 0x00};
    
    // Calculate CRC (simplified - using fixed values for common registers)
    if (slaveId == 1) {
      frame[6] = 0x55; frame[7] = 0xCA;
    } else if (slaveId == 5) {
      frame[6] = 0x54; frame[7] = 0x0A;
    } else if (slaveId == 10) {
      frame[6] = 0x55; frame[7] = 0x6E;
    } else if (slaveId == 247) {
      frame[6] = 0x50; frame[7] = 0x1F;
    }
    
    // Clear buffer
    while (Serial2.available()) {
      Serial2.read();
    }
    
    // Send frame
    for (int i = 0; i < 8; i++) {
      Serial2.write(frame[i]);
    }
    
    delay(500);
    
    if (Serial2.available()) {
      Serial.print("   -> Response: ");
      while (Serial2.available()) {
        uint8_t data = Serial2.read();
        Serial.print("0x");
        Serial.print(data, HEX);
        Serial.print(" ");
      }
      Serial.println();
    } else {
      Serial.println("   -> No response");
    }
  }
  
  Serial.println("\n4. Continuous Monitoring Test...");
  Serial.println("   Monitoring for any incoming data for 10 seconds...");
  
  unsigned long startTime = millis();
  bool dataReceived = false;
  
  while (millis() - startTime < 10000) {
    if (Serial2.available()) {
      if (!dataReceived) {
        Serial.print("   -> Data stream detected: ");
        dataReceived = true;
      }
      uint8_t data = Serial2.read();
      Serial.print("0x");
      Serial.print(data, HEX);
      Serial.print(" ");
    }
    delay(10);
  }
  
  if (!dataReceived) {
    Serial.println("   -> No spontaneous data detected");
  } else {
    Serial.println();
  }
  
  Serial.println("\n=== Hardware Test Results ===");
  Serial.println("If you see responses to Modbus frames, the connection is working.");
  Serial.println("If you see no responses at all, check:");
  Serial.println("1. Physical wiring (TX/RX might be swapped)");
  Serial.println("2. RS232 level converter");
  Serial.println("3. Power supply to converter");
  Serial.println("4. Inverter Modbus settings");
  Serial.println("5. Try different baud rates and slave IDs");
  
  Serial.println("\nExpected response format for successful read:");
  Serial.println("[Slave ID][Function][Byte Count][Data H][Data L][CRC L][CRC H]");
  Serial.println("Example: 0x05 0x03 0x02 0x21 0x5C 0x?? 0x??");
  
  Serial2.end();
}
